<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动化生图工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-magic"></i> 自动化生图工具</h1>
            <div class="header-controls">
                <button id="logToggle" class="btn btn-outline">
                    <i class="fas fa-list"></i> 日志
                </button>
                <button id="configBtn" class="btn btn-outline">
                    <i class="fas fa-cog"></i> 配置
                </button>
            </div>
        </header>

        <nav class="nav-tabs">
            <button class="nav-tab active" data-tab="story">
                <i class="fas fa-book"></i> 一键生成故事
            </button>
            <button class="nav-tab" data-tab="character-gen">
                <i class="fas fa-user-plus"></i> 角色形象生成
            </button>
            <button class="nav-tab" data-tab="imagehost">
                <i class="fas fa-cloud-upload-alt"></i> 图床
            </button>
            <button class="nav-tab" data-tab="excel">
                <i class="fas fa-file-excel"></i> Excel模块
            </button>
            <button class="nav-tab" data-tab="characters">
                <i class="fas fa-users"></i> 角色参考图配置
            </button>
            <button class="nav-tab" data-tab="models">
                <i class="fas fa-server"></i> 模型服务配置
            </button>
        </nav>

        <main class="main-content">
            <!-- 一键生成故事 -->
            <div id="story-tab" class="tab-content active">
                <div class="function-area">
                    <div class="control-group">
                        <div class="file-upload">
                            <input type="file" id="excelFile" accept=".xlsx,.xls" style="display: none;">
                            <button id="selectExcelBtn" class="btn btn-primary">
                                <i class="fas fa-file-excel"></i> 选择生图Excel文件
                            </button>
                            <span id="selectedFileName" class="file-name"></span>
                        </div>
                    </div>

                    <div class="control-group">
                        <div class="select-group">
                            <label>模型服务:</label>
                            <select id="modelService" class="select-input">
                                <option value="">请选择...</option>
                            </select>
                        </div>
                        <div class="select-group">
                            <label>生图模型:</label>
                            <select id="imageModel" class="select-input">
                                <option value="">请选择...</option>
                            </select>
                        </div>
                    </div>

                    <div class="control-group">
                        <div class="input-group">
                            <label>每个分镜批量生图数量:</label>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <input type="number" id="batchCount" value="1" min="1" max="10" class="number-input">
                                <button id="applyBatchCount" class="btn btn-sm btn-secondary">确认</button>
                            </div>
                        </div>
                        <div class="input-group">
                            <label>保存目录:</label>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <input type="text" id="saveDirectory" value="D:\\BaiduSyncdisk\\Youtube\\wcs_util" class="text-input">
                                <button id="selectDirectoryBtn" class="btn btn-sm btn-outline">选择</button>
                            </div>
                        </div>
                        <div class="input-group">
                            <label>角色核心特征:</label>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <label class="switch">
                                    <input type="checkbox" id="globalCoreFeatures" checked>
                                    <span class="slider"></span>
                                </label>
                                <span>启用角色核心特征</span>
                                <button id="applyCoreFeatures" class="btn btn-sm btn-secondary">应用到所有分镜</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-group">
                        <button id="exportExcelBtn" class="btn btn-outline">
                            <i class="fas fa-download"></i> 导出Excel文件
                        </button>
                        <button id="generateAllBtn" class="btn btn-success" disabled>
                            <i class="fas fa-play"></i> 一键生成所有分镜
                        </button>
                        <button id="saveCacheBtn" class="btn btn-info" disabled>
                            <i class="fas fa-save"></i> 保存缓存
                        </button>
                        <button id="clearDataBtn" class="btn btn-danger" disabled>
                            <i class="fas fa-trash"></i> 清空数据
                        </button>
                    </div>
                </div>

                <div class="progress-container" id="progressContainer" style="display: none;">
                    <div class="progress-header">
                        <h4>批量生成进度</h4>
                        <span class="progress-time" id="batchProgressTime">准备中...</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text">
                        <span id="progressText">准备中...</span>
                        <span id="estimatedTime"></span>
                    </div>
                    <div class="batch-progress-details" id="batchProgressDetails">
                        <!-- 动态生成的分镜进度 -->
                    </div>
                </div>

                <div id="scenesContainer" class="scenes-container">
                    <!-- 分镜将在这里动态生成 -->
                </div>
            </div>

            <!-- 角色形象生成 -->
            <div id="character-gen-tab" class="tab-content">
                <div class="function-area">
                    <div class="control-group">
                        <div class="select-group">
                            <label>模型服务:</label>
                            <select id="charGenModelService" class="select-input">
                                <option value="">请选择...</option>
                            </select>
                        </div>

                        <div class="select-group">
                            <label>生图模型:</label>
                            <select id="charGenImageModel" class="select-input">
                                <option value="">请选择...</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label>生图数量:</label>
                            <input type="number" id="charGenImageCount" class="number-input" value="1" min="1" max="10">
                            <button id="applyCharGenImageCountBtn" class="btn btn-outline btn-sm">
                                <i class="fas fa-check"></i> 应用到所有
                            </button>
                        </div>

                        <div class="input-group">
                            <label>保存目录:</label>
                            <input type="text" id="charGenSaveDir" class="text-input" placeholder="请输入保存目录">
                            <button id="charGenBrowseDirBtn" class="btn btn-outline btn-sm">
                                <i class="fas fa-folder-open"></i> 浏览
                            </button>
                        </div>
                    </div>

                    <div class="control-group">
                        <button id="addCharGenPromptBtn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 添加角色提示词
                        </button>
                        <button id="charGenGenerateAllBtn" class="btn btn-success" disabled>
                            <i class="fas fa-play"></i> 开始生图
                        </button>
                        <button id="charGenSaveCacheBtn" class="btn btn-info" disabled>
                            <i class="fas fa-save"></i> 保存缓存
                        </button>
                        <button id="charGenClearDataBtn" class="btn btn-danger" disabled>
                            <i class="fas fa-trash"></i> 清空数据
                        </button>
                    </div>
                </div>

                <div class="progress-container" id="charGenProgressContainer" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="charGenProgressFill"></div>
                    </div>
                    <div class="progress-text">
                        <span id="charGenProgressText">准备中...</span>
                        <span id="charGenEstimatedTime"></span>
                    </div>
                </div>

                <div id="charGenPromptsContainer" class="scenes-container">
                    <!-- 角色提示词将在这里动态生成 -->
                </div>
            </div>

            <!-- 模型服务配置 -->
            <div id="models-tab" class="tab-content">
                <div class="config-section">
                    <div class="section-header">
                        <h3>模型服务配置</h3>
                        <button id="addServiceBtn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 添加服务
                        </button>
                    </div>
                    <div id="servicesContainer">
                        <!-- 服务配置将在这里动态生成 -->
                    </div>
                </div>

                <!-- 图片保存设置 -->
                <div class="config-section">
                    <div class="section-header">
                        <h3>图片保存设置</h3>
                        <button id="saveSaveSettingsBtn" class="btn btn-success">
                            <i class="fas fa-save"></i> 保存设置
                        </button>
                    </div>
                    <div class="save-settings-container">
                        <div class="setting-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="preventOverwriteCheck" checked>
                                防止文件覆盖
                            </label>
                            <small class="setting-description">启用后，重复生成时会自动生成新的文件名，避免覆盖已有文件</small>
                        </div>

                        <div class="setting-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="addTimestampCheck" checked>
                                文件名包含时间戳
                            </label>
                            <small class="setting-description">在文件名中添加时间戳，便于区分不同批次的生成结果</small>
                        </div>

                        <div class="setting-group">
                            <label>文件命名格式:</label>
                            <select id="filenameFormatSelect" class="select-input">
                                <option value="timestamp">包含时间戳 (推荐)</option>
                                <option value="simple">简单格式</option>
                            </select>
                            <small class="setting-description">
                                时间戳格式: excel名_分镜号_时间戳_图片序号.png<br>
                                简单格式: excel名_分镜号_图片序号.png
                            </small>
                        </div>

                        <div class="setting-group">
                            <div class="setting-preview">
                                <label>文件名预览:</label>
                                <div class="filename-preview" id="filenamePreview">
                                    test_1_20250729_143022_1.png
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 角色参考图配置 -->
            <div id="characters-tab" class="tab-content">
                <div class="config-section">
                    <div class="section-header">
                        <h3>角色参考图配置</h3>
                        <button id="addCharacterBtn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 添加角色
                        </button>
                    </div>
                    <div id="charactersContainer">
                        <!-- 角色配置将在这里动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 图床 -->
            <div id="imagehost-tab" class="tab-content">
                <div class="config-section">
                    <div class="section-header">
                        <h3>GitHub图床配置</h3>
                        <button id="saveImageHostBtn" class="btn btn-success">
                            <i class="fas fa-save"></i> 保存配置
                        </button>
                    </div>

                    <div class="imagehost-config">
                        <div class="form-group">
                            <label for="githubToken">GitHub API Token:</label>
                            <input type="password" id="githubToken" class="form-input" placeholder="请输入GitHub Personal Access Token">
                            <small class="form-note">需要repo权限的Personal Access Token</small>
                        </div>

                        <div class="form-group">
                            <label for="repoPath">仓库路径:</label>
                            <input type="text" id="repoPath" class="form-input" value="/scys-wcs/photo/contents/image" placeholder="/username/repo/contents/path">
                            <small class="form-note">格式: /用户名/仓库名/contents/路径</small>
                        </div>

                        <div class="form-group">
                            <label for="maxFileSize">最大文件大小 (MB):</label>
                            <input type="number" id="maxFileSize" class="form-input" value="3" min="1" max="25" placeholder="3">
                            <small class="form-note">GitHub单文件限制25MB，建议3MB以内</small>
                        </div>
                    </div>

                    <div class="upload-section">
                        <h4>上传图片</h4>
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <p>点击选择图片或拖拽图片到此处</p>
                                <p class="upload-hint">支持 JPG、PNG、GIF 格式</p>
                            </div>
                            <input type="file" id="imageFile" accept="image/*" style="display: none;">
                        </div>

                        <div class="upload-progress" id="uploadProgress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" id="uploadProgressFill"></div>
                            </div>
                            <div class="progress-text" id="uploadProgressText">准备上传...</div>
                        </div>

                        <div class="upload-result" id="uploadResult" style="display: none;">
                            <h5>上传结果:</h5>
                            <div class="result-content">
                                <img id="uploadedImage" src="" alt="上传的图片" style="max-width: 200px; max-height: 200px;">
                                <div class="result-links">
                                    <div class="link-group">
                                        <label>图片链接:</label>
                                        <div class="link-input-group">
                                            <input type="text" id="imageUrl" class="form-input" readonly>
                                            <button class="btn btn-outline" onclick="app.copyToClipboard('imageUrl')">
                                                <i class="fas fa-copy"></i> 复制
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 上传历史记录 -->
                    <div class="upload-history-section">
                        <div class="section-header">
                            <h4>上传历史记录</h4>
                            <div class="header-controls">
                                <button id="refreshHistoryBtn" class="btn btn-outline btn-sm">
                                    <i class="fas fa-refresh"></i> 刷新
                                </button>
                                <button id="clearHistoryBtn" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i> 清空历史
                                </button>
                            </div>
                        </div>
                        <div class="upload-history-container" id="uploadHistoryContainer">
                            <div class="history-loading" id="historyLoading">
                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                            </div>
                            <div class="history-empty" id="historyEmpty" style="display: none;">
                                <i class="fas fa-image"></i>
                                <p>暂无上传记录</p>
                            </div>
                            <div class="history-list" id="historyList">
                                <!-- 历史记录将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Excel模块 -->
            <div id="excel-tab" class="tab-content">
                <div class="excel-modules-container">
                    <!-- 左侧：TXT转Excel模块 -->
                    <div class="excel-module">
                        <div class="config-section">
                            <div class="section-header">
                                <h3>TXT转Excel</h3>
                                <div class="header-controls">
                                    <button id="clearExcelDataBtn" class="btn btn-danger" style="display: none;">
                                        <i class="fas fa-trash"></i> 清空数据
                                    </button>
                                </div>
                            </div>

                            <div class="excel-config">
                                <div class="form-group">
                                    <label>保存目录:</label>
                                    <div style="display: flex; gap: 10px; align-items: center;">
                                        <input type="text" id="excelSaveDir" class="form-input" placeholder="请输入保存目录">
                                        <button id="excelBrowseDirBtn" class="btn btn-outline">
                                            <i class="fas fa-folder-open"></i> 浏览
                                        </button>
                                    </div>
                                    <small class="form-note">与一键生成故事、角色形象生成使用相同的配置目录</small>
                                </div>
                            </div>

                            <div class="upload-section">
                                <h4>上传TXT文本</h4>
                                <div class="upload-area" id="txtUploadArea">
                                    <div class="upload-content">
                                        <i class="fas fa-file-text upload-icon"></i>
                                        <p>点击选择TXT文件或拖拽文件到此处</p>
                                        <p class="upload-hint">支持 TXT 格式文本文件</p>
                                    </div>
                                    <input type="file" id="txtFile" accept=".txt" style="display: none;">
                                </div>

                                <div class="upload-progress" id="txtUploadProgress" style="display: none;">
                                    <div class="progress-bar">
                                        <div class="progress-fill" id="txtUploadProgressFill"></div>
                                    </div>
                                    <div class="progress-text" id="txtUploadProgressText">准备处理...</div>
                                </div>

                                <div class="upload-result" id="txtUploadResult" style="display: none;">
                                    <h5>处理结果:</h5>
                                    <div class="result-content">
                                        <div class="result-info">
                                            <p id="txtProcessResult"></p>
                                            <div class="result-actions" style="margin-top: 15px;">
                                                <button id="downloadExcelBtn" class="btn btn-success" style="display: none;">
                                                    <i class="fas fa-download"></i> 下载Excel文件
                                                </button>
                                                <button id="openExcelDirBtn" class="btn btn-outline" style="display: none;">
                                                    <i class="fas fa-folder-open"></i> 打开保存目录
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：查看Excel模块 -->
                    <div class="excel-module">
                        <div class="config-section">
                            <div class="section-header">
                                <h3>查看Excel</h3>
                            </div>

                            <div class="excel-viewer-upload">
                                <div class="upload-section">
                                    <h4>上传Excel文件</h4>
                                    <div class="upload-area" id="excelViewerUploadArea">
                                        <div class="upload-content">
                                            <i class="fas fa-file-excel upload-icon"></i>
                                            <p>点击选择Excel文件或拖拽文件到此处</p>
                                            <p class="upload-hint">支持 .xlsx 和 .xls 格式文件</p>
                                        </div>
                                        <input type="file" id="excelViewerFile" accept=".xlsx,.xls" style="display: none;">
                                    </div>
                                </div>
                            </div>

                            <!-- Excel数据显示区域 -->
                            <div class="excel-viewer-content" id="excelViewerContent" style="display: none;">
                                <div class="viewer-header">
                                    <div class="file-info">
                                        <span id="excelViewerFileName" class="file-name">未选择文件</span>
                                        <span id="excelViewerFileInfo" class="file-info-text"></span>
                                    </div>
                                    <div class="viewer-actions">
                                        <button id="exportViewerExcelBtn" class="btn btn-success" disabled>
                                            <i class="fas fa-download"></i> 导出Excel
                                        </button>
                                        <button id="clearViewerDataBtn" class="btn btn-secondary" disabled>
                                            <i class="fas fa-trash"></i> 清空数据
                                        </button>
                                    </div>
                                </div>

                                <div class="excel-table-container" id="excelTableContainer">
                                    <div class="empty-state">请上传Excel文件</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 日志面板 -->
        <div id="logPanel" class="log-panel">
            <div class="log-header">
                <h4><i class="fas fa-list"></i> 系统日志</h4>
                <div class="log-header-controls">
                    <button id="clearLogsBtn" class="btn btn-sm">清空</button>
                    <button id="closeLogsBtn" class="btn btn-sm">关闭</button>
                </div>
            </div>
            <div id="logContent" class="log-content">
                <!-- 日志内容 -->
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modalTitle">标题</h4>
                <button id="modalClose" class="modal-close">&times;</button>
            </div>
            <div id="modalBody" class="modal-body">
                <!-- 模态框内容 -->
            </div>
            <div class="modal-footer">
                <button id="modalCancel" class="btn btn-outline">取消</button>
                <button id="modalApplyAll" class="btn btn-secondary" style="display: none;">应用到所有分镜</button>
                <button id="modalConfirm" class="btn btn-primary">确认</button>
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div id="imageModal" class="modal">
        <div class="modal-content image-modal-content">
            <div class="modal-header">
                <h4>图片预览</h4>
                <button id="imageModalClose" class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="image-preview-container">
                    <button id="prevImageBtn" class="image-nav-btn prev-btn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <img id="previewImage" src="" alt="预览图片">
                    <button id="nextImageBtn" class="image-nav-btn next-btn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <div class="image-info">
                        <span id="imageInfo">1 / 1</span>
                    </div>
                    <div class="image-controls">
                        <div class="zoom-controls">
                            <button id="zoomOut" class="btn btn-sm btn-outline">
                                <i class="fas fa-search-minus"></i> 缩小
                            </button>
                            <span id="zoomLevel">100%</span>
                            <button id="zoomIn" class="btn btn-sm btn-outline">
                                <i class="fas fa-search-plus"></i> 放大
                            </button>
                        </div>
                        <button id="resetZoom" class="btn btn-sm btn-secondary">
                            <i class="fas fa-expand-arrows-alt"></i> 适应屏幕
                        </button>
                        <button id="downloadImage" class="btn btn-sm btn-primary">
                            <i class="fas fa-download"></i> 下载
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>