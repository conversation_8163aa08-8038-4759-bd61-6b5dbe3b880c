#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def create_test_excel():
    """创建一个测试用的Excel文件"""
    
    # 创建测试数据
    data = {
        '角色名称': ['小明', '小红', '小刚', '小丽', '小华'],
        '年龄': [25, 23, 28, 26, 24],
        '职业': ['程序员', '设计师', '产品经理', '运营专员', '数据分析师'],
        'prompt': [
            '一个年轻的程序员，戴着眼镜，坐在电脑前专注地编写代码，桌上放着咖啡杯和技术书籍',
            '一位创意十足的设计师，手持画笔，正在设计板上绘制精美的插画作品',
            '经验丰富的产品经理，正在会议室里向团队展示产品原型和发展规划',
            '活泼开朗的运营专员，正在分析用户数据，制定营销策略和推广方案',
            '细心严谨的数据分析师，面对多个显示器，正在处理复杂的数据图表'
        ],
        '提示词': [
            'young programmer, glasses, coding at computer, coffee cup, technical books',
            'creative designer, paintbrush, drawing illustration on design board',
            'experienced product manager, presenting prototype in meeting room',
            'cheerful operations specialist, analyzing user data, marketing strategy',
            'meticulous data analyst, multiple monitors, complex data charts'
        ],
        '部门': ['技术部', '设计部', '产品部', '运营部', '数据部'],
        '入职时间': ['2023-01-15', '2023-03-20', '2022-11-10', '2023-05-08', '2023-02-28'],
        '薪资': [15000, 12000, 18000, 10000, 14000],
        '备注': ['熟悉Python和JavaScript', '擅长UI/UX设计', '有5年产品经验', '社交媒体运营专家', 'SQL和Python专家']
    }
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 保存为Excel文件
    output_file = 'test_excel_data.xlsx'
    df.to_excel(output_file, index=False, engine='openpyxl')
    
    print(f"测试Excel文件已创建: {output_file}")
    print(f"文件大小: {os.path.getsize(output_file)} 字节")
    print(f"数据行数: {len(df)} 行")
    print(f"数据列数: {len(df.columns)} 列")
    print("\n列名:")
    for i, col in enumerate(df.columns, 1):
        print(f"  {i}. {col}")

if __name__ == '__main__':
    create_test_excel()
