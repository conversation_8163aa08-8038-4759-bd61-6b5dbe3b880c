import pandas as pd
import os
import json
from typing import Dict, Any, List, Optional
from werkzeug.datastructures import FileStorage

class ExcelViewer:
    def __init__(self):
        self.current_data = None
        self.current_filename = None
        self.column_widths = {}
    
    def read_excel_file(self, file: FileStorage) -> Dict[str, Any]:
        """读取Excel文件并返回数据"""
        try:
            if file.filename.endswith('.xlsx'):
                df = pd.read_excel(file, engine='openpyxl')
            elif file.filename.endswith('.xls'):
                df = pd.read_excel(file, engine='xlrd')
            else:
                raise ValueError("Unsupported file format. Please use .xlsx or .xls files.")
            
            # 处理NaN值
            df = df.fillna('')

            # 转换为字典格式，确保所有数据类型都是JSON可序列化的
            rows = []
            for _, row in df.iterrows():
                row_data = []
                for value in row:
                    # 处理各种数据类型，确保JSON序列化兼容
                    if pd.isna(value):
                        row_data.append("")
                    elif isinstance(value, (int, float)):
                        # 转换numpy数值类型为Python原生类型
                        if pd.isna(value):
                            row_data.append("")
                        else:
                            # 使用Python原生类型
                            row_data.append(float(value) if isinstance(value, float) else int(value))
                    else:
                        row_data.append(str(value))
                rows.append(row_data)

            data = {
                'columns': df.columns.tolist(),
                'rows': rows,
                'filename': file.filename,
                'row_count': len(df),
                'column_count': len(df.columns)
            }
            
            # 分析列类型，为包含"prompt"或"提示词"的列设置特殊标记
            column_info = {}
            for col in df.columns:
                col_str = str(col).lower()
                is_long_text = any(keyword in col_str for keyword in ['prompt', '提示词', '描述', 'description'])
                column_info[col] = {
                    'is_long_text': is_long_text,
                    'max_length': df[col].astype(str).str.len().max() if not df.empty else 0
                }
            
            data['column_info'] = column_info
            
            self.current_data = data
            self.current_filename = file.filename
            
            return data
            
        except Exception as e:
            raise Exception(f"Error reading Excel file: {str(e)}")
    
    def update_cell_data(self, row_index: int, column_index: int, new_value: str) -> Dict[str, Any]:
        """更新单元格数据"""
        try:
            if self.current_data is None:
                raise Exception("No Excel data loaded")
            
            if row_index < 0 or row_index >= len(self.current_data['rows']):
                raise Exception("Invalid row index")
            
            if column_index < 0 or column_index >= len(self.current_data['columns']):
                raise Exception("Invalid column index")
            
            # 更新数据
            self.current_data['rows'][row_index][column_index] = new_value
            
            return {
                'success': True,
                'row_index': row_index,
                'column_index': column_index,
                'new_value': new_value
            }
            
        except Exception as e:
            raise Exception(f"Error updating cell data: {str(e)}")
    
    def export_to_excel(self, filename: str = None) -> str:
        """导出当前数据为Excel文件"""
        try:
            if self.current_data is None:
                raise Exception("No Excel data to export")
            
            # 创建DataFrame
            df = pd.DataFrame(self.current_data['rows'], columns=self.current_data['columns'])
            
            # 确定文件名
            if filename is None:
                base_name = os.path.splitext(self.current_filename)[0] if self.current_filename else 'exported'
                filename = f"{base_name}_edited.xlsx"
            
            if not filename.endswith('.xlsx'):
                filename = filename.replace('.xls', '') + '.xlsx'
            
            # 确保导出目录存在
            export_dir = os.path.join(os.getcwd(), "exports")
            os.makedirs(export_dir, exist_ok=True)
            
            export_path = os.path.join(export_dir, filename)
            
            # 导出Excel文件
            with pd.ExcelWriter(export_path, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Sheet1')
                
                # 获取工作表并调整列宽
                worksheet = writer.sheets['Sheet1']
                
                # 根据列信息调整列宽
                for i, col in enumerate(self.current_data['columns']):
                    column_letter = chr(65 + i) if i < 26 else f"A{chr(65 + i - 26)}"
                    
                    # 获取列信息
                    col_info = self.current_data.get('column_info', {}).get(col, {})
                    
                    if col_info.get('is_long_text', False):
                        # 长文本列设置较大宽度
                        width = min(max(col_info.get('max_length', 20) * 0.8, 30), 100)
                    else:
                        # 普通列根据内容长度调整
                        width = min(max(col_info.get('max_length', 10) * 1.2, 15), 50)
                    
                    worksheet.column_dimensions[column_letter].width = width
                
                # 设置文本换行
                from openpyxl.styles import Alignment
                for row in worksheet.iter_rows():
                    for cell in row:
                        cell.alignment = Alignment(wrap_text=True, vertical='top')
            
            return export_path
            
        except Exception as e:
            raise Exception(f"Error exporting Excel file: {str(e)}")
    
    def get_current_data(self) -> Dict[str, Any]:
        """获取当前数据"""
        return self.current_data
    
    def clear_data(self):
        """清空当前数据"""
        self.current_data = None
        self.current_filename = None
        self.column_widths = {}
