# Excel查看器模块使用说明

## 功能概述

Excel查看器模块是一个全新的功能模块，位于Excel标签页的右侧，与现有的"TXT转Excel"模块并列显示。该模块提供了完整的Excel文件查看、编辑和导出功能。

## 主要功能

### 1. 文件上传
- **支持格式**: .xlsx 和 .xls 文件
- **上传方式**: 
  - 点击上传区域选择文件
  - 拖拽文件到上传区域
- **文件验证**: 自动验证文件格式和完整性

### 2. 智能表格渲染
- **自动列宽调整**: 
  - 包含"prompt"或"提示词"的列自动设置为300px宽度
  - 其他列默认150px宽度
- **响应式设计**: 支持不同屏幕尺寸的自适应显示
- **粘性表头**: 滚动时表头保持可见

### 3. 交互式编辑
- **单元格编辑**: 点击任意单元格进行编辑
- **智能编辑器**: 
  - 长文本列使用多行文本框
  - 普通列使用单行输入框
- **快捷键支持**:
  - `Enter`: 保存编辑（单行输入）
  - `Escape`: 取消编辑
  - `Tab`: 移动到下一个单元格

### 4. 可调整大小
- **列宽调整**: 拖拽列边界调整列宽
- **行高调整**: 拖拽行边界调整行高
- **最小尺寸限制**: 防止调整过小影响使用

### 5. 数据导出
- **保持格式**: 导出时保留所有编辑内容
- **自动下载**: 点击导出按钮自动下载文件
- **文件命名**: 支持自定义导出文件名

## 使用步骤

### 步骤1: 上传Excel文件
1. 点击Excel标签页
2. 在右侧"查看Excel"模块中点击上传区域
3. 选择.xlsx或.xls文件，或直接拖拽文件到上传区域

### 步骤2: 查看和编辑数据
1. 文件上传成功后，数据将以表格形式显示
2. 点击任意单元格开始编辑
3. 使用鼠标拖拽调整列宽和行高
4. 编辑完成后点击其他区域或按Enter保存

### 步骤3: 导出修改后的文件
1. 点击"导出Excel"按钮
2. 文件将自动下载到默认下载目录
3. 导出的文件包含所有修改内容

## 技术特性

### 前端技术
- **HTML5**: 现代化的文件上传和拖拽支持
- **CSS3**: 响应式布局和动画效果
- **JavaScript**: 交互式编辑和实时更新

### 后端技术
- **Flask**: RESTful API接口
- **pandas**: 数据处理和分析
- **openpyxl**: Excel文件读写

### 性能优化
- **懒加载**: 大文件分批渲染
- **内存管理**: 及时释放不需要的数据
- **缓存机制**: 减少重复计算

## 注意事项

1. **文件大小限制**: 建议上传文件不超过10MB
2. **浏览器兼容性**: 推荐使用Chrome、Firefox、Edge等现代浏览器
3. **数据安全**: 文件仅在本地处理，不会上传到外部服务器
4. **编辑保存**: 编辑后需要手动导出才能保存到文件

## 故障排除

### 常见问题
1. **文件上传失败**: 检查文件格式是否为.xlsx或.xls
2. **表格显示异常**: 刷新页面重新上传文件
3. **编辑无法保存**: 确保点击其他区域或按Enter键保存
4. **导出失败**: 检查浏览器下载设置

### 技术支持
如遇到其他问题，请检查浏览器控制台的错误信息，或联系技术支持。

## 更新日志

### v1.0.0 (2025-07-30)
- 初始版本发布
- 支持Excel文件上传、查看、编辑和导出
- 实现可拖拽调整大小的表格
- 添加智能列宽检测功能
- 支持响应式设计
