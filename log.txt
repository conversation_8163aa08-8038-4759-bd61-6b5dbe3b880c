2025-07-30 10:28:37,704 - INFO - 127.0.0.1 - - [30/Jul/2025 10:28:37] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 10:28:37,719 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f] Starting batch generation for 2 scenes (total: 2)
2025-07-30 10:28:37,720 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f] 开始并发处理 2 个分镜，最大并发数: 2
2025-07-30 10:28:37,720 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1] 开始并发处理分镜 15
2025-07-30 10:28:37,720 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2] 开始并发处理分镜 16
2025-07-30 10:28:37,720 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1] 开始单个分镜生成: 15
2025-07-30 10:28:37,721 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2] 开始单个分镜生成: 16
2025-07-30 10:28:37,721 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1] 生成参数: service_id=1, model=sora_image, prompt='主体: Ballerina Cappuccina Baby; 细节: 穿着小恐龙连体衣的宝宝被完全封...', image_count=2
2025-07-30 10:28:37,721 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2] 生成参数: service_id=1, model=sora_image, prompt='主体:可爱Ballerina Cappuccina Baby; 细节: 穿着柔软的小恐龙连体衣的宝宝...', image_count=2
2025-07-30 10:28:37,721 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1] 开始并发生成 2 张图片，最大并发数: 2
2025-07-30 10:28:37,722 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2] 开始并发生成 2 张图片，最大并发数: 2
2025-07-30 10:28:37,722 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] 并发生成第 1/2 张图片
2025-07-30 10:28:37,722 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] 并发生成第 2/2 张图片
2025-07-30 10:28:37,722 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] API请求载荷构建完成
2025-07-30 10:28:37,723 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] API请求载荷构建完成
2025-07-30 10:28:37,723 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] 并发生成第 1/2 张图片
2025-07-30 10:28:37,723 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] 并发生成第 2/2 张图片
2025-07-30 10:28:37,724 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] API请求载荷构建完成
2025-07-30 10:28:37,724 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 10:28:37,724 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] API请求载荷构建完成
2025-07-30 10:28:37,724 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 10:28:37,725 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] 
=== API调用开始 [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1_api_6f4c373a] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。主体: Ballerina Cappuccina Baby; 细节: 穿着小恐龙连体衣的宝宝被完全封在一个巨大的、正方体的透明冰块中，保持着蜷缩的姿势，双眼紧闭，脸上凝固着痛苦的表情，但是宝宝被封在冰块中，整体又看起来特别可爱。冰块表面有气泡和冰裂纹; 环境: 冰箱抽屉内部，背景是冰箱的白色内壁; 时间: 中午; 天气: 极寒; 构图: 强调被冰封的视觉冲击力; 画风: 照片级真实感，3D动画风格; 光影: 冰块折射着冰箱内的冷光，显得晶莹而致命; 参数: 画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 10:28:37,725 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 10:28:37,726 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] 
=== API调用开始 [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2_api_75bb8133] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。主体: Ballerina Cappuccina Baby; 细节: 穿着小恐龙连体衣的宝宝被完全封在一个巨大的、正方体的透明冰块中，保持着蜷缩的姿势，双眼紧闭，脸上凝固着痛苦的表情，但是宝宝被封在冰块中，整体又看起来特别可爱。冰块表面有气泡和冰裂纹; 环境: 冰箱抽屉内部，背景是冰箱的白色内壁; 时间: 中午; 天气: 极寒; 构图: 强调被冰封的视觉冲击力; 画风: 照片级真实感，3D动画风格; 光影: 冰块折射着冰箱内的冷光，显得晶莹而致命; 参数: 画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 10:28:37,726 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 10:28:37,728 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] 
=== API调用开始 [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1_api_e2ed2210] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\n3. 角色年龄是1~3岁的婴儿。\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体:可爱Ballerina Cappuccina Baby; 细节: 穿着柔软的小恐龙连体衣的宝宝被完全封在一个巨大的、不规则的透明冰块中，身体蜷缩，双眼紧闭，脸上带着一丝委屈和可爱的表情，例如微微嘟起的嘴唇和长长的、湿润的睫毛。冰块内部可见细小的气泡，表面有自然的冰裂纹; 环境: 冰箱抽屉内部的特写，清晰可见冰箱的白色塑料内壁; 时间: 中午; 天气: 极寒; 构图: 超近特写，着重表现宝宝被冰封的脆弱和可爱; 画风: 超逼真照片级，略带柔和的3D动画质感; 光影: 冰块反射着冰箱内部略显柔和的冷光，营造出晶莹剔透但又寒冷的环境; 参数: 极致的细节和清晰度，照片级真实感，细腻的皮肤和连体衣纹理，冰块和环境的材质都栩栩如生。自然的光影过渡，准确的色彩还原，无噪点，无失真，极致的8K分辨率。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 10:28:37,731 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] 
=== API调用开始 [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2_api_0bc4a768] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\n3. 角色年龄是1~3岁的婴儿。\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体:可爱Ballerina Cappuccina Baby; 细节: 穿着柔软的小恐龙连体衣的宝宝被完全封在一个巨大的、不规则的透明冰块中，身体蜷缩，双眼紧闭，脸上带着一丝委屈和可爱的表情，例如微微嘟起的嘴唇和长长的、湿润的睫毛。冰块内部可见细小的气泡，表面有自然的冰裂纹; 环境: 冰箱抽屉内部的特写，清晰可见冰箱的白色塑料内壁; 时间: 中午; 天气: 极寒; 构图: 超近特写，着重表现宝宝被冰封的脆弱和可爱; 画风: 超逼真照片级，略带柔和的3D动画质感; 光影: 冰块反射着冰箱内部略显柔和的冷光，营造出晶莹剔透但又寒冷的环境; 参数: 极致的细节和清晰度，照片级真实感，细腻的皮肤和连体衣纹理，冰块和环境的材质都栩栩如生。自然的光影过渡，准确的色彩还原，无噪点，无失真，极致的8K分辨率。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 10:30:50,740 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] API响应状态: 200, 耗时: 133.01秒
2025-07-30 10:30:50,740 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] 
=== API调用响应 [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2_api_75bb8133] ===
状态码: 200
响应时间: 133.01秒
响应内容: {
  "id": "foaicmpl-eaccc962-97c0-46d4-bf33-5fa3cad7cb2f",
  "model": "sora_image",
  "object": "chat.completion",
  "created": 1753842529,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/b8e4036b-fc99-48b3-b420-9c2d5a3d7e30.png)\n\n"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 1558,
    "completion_tokens": 51,
    "total_tokens": 1609,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "text_tokens": 0,
      "audio_tokens": 0,
      "image_tokens": 0
    },
    "completion_tokens_details": {
      "text_tokens": 0,
      "audio_tokens": 0,
      "reasoning_tokens": 0
    },
    "input_tokens": 0,
    "output_tokens": 0,
    "input_tokens_details": null
  }
}
=== API调用结束 [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2_api_75bb8133] ===

2025-07-30 10:30:50,745 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] API调用成功，开始提取图片URL
2025-07-30 10:30:50,745 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] 提取到 1 个图片URL
2025-07-30 10:30:50,746 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] URL去重: 1 -> 1
2025-07-30 10:30:52,835 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] 图片MD5: 75ae64b2... - 保留
2025-07-30 10:30:52,836 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] 内容去重: 1 -> 1
2025-07-30 10:30:52,837 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] 去重后剩余 1 个图片
2025-07-30 10:30:52,862 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_2] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\冰块遗漏分镜_分镜15_1753842652837.png
2025-07-30 10:30:52,863 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1] 批次 2 完成，生成 1 张图片
2025-07-30 10:31:30,092 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] API响应状态: 200, 耗时: 172.36秒
2025-07-30 10:31:30,094 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] 
=== API调用响应 [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2_api_0bc4a768] ===
状态码: 200
响应时间: 172.36秒
响应内容: {
  "id": "chatcmpl-89DtEzsIiwxlRXZER1JXLwbSNiZXN",
  "object": "chat.completion",
  "created": 1753842689,
  "model": "sora_image",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "```json\n{\n  \"prompt\": \"Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\\n3. 角色年龄是1~3岁的婴儿。\\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体:可爱Ballerina Cappuccina Baby; 细节: 穿着柔软的小恐龙连体衣的宝宝被完全封在一个巨大的、不规则的透明冰块中，身体蜷缩，双眼紧闭，脸上带着一丝委屈和可爱的表情，例如微微嘟起的嘴唇和长长的、湿润的睫毛。冰块内部可见细小的气泡，表面有自然的冰裂纹; 环境: 冰箱抽屉内部的特写，清晰可见冰箱的白色塑料内壁; 时间: 中午; 天气: 极寒; 构图: 超近特写，着重表现宝宝被冰封的脆弱和可爱; 画风: 超逼真照片级，略带柔和的3D动画质感; 光影: 冰块反射着冰箱内部略显柔和的冷光，营造出晶莹剔透但又寒冷的环境; 参数: 极致的细节和清晰度，照片级真实感，细腻的皮肤和连体衣纹理，冰块和环境的材质都栩栩如生。自然的光影过渡，准确的色彩还原，无噪点，无失真，极致的8K分辨率。\",\n  \"ratio\": \"9:16\",\n  \"n\": 1\n}\n```\n\n>🕐 排队中.\n\n>⚡ 生成中.......\n\n>🏃‍ 进度 3.....11.....18.....26.....34.....41.....48.....55.....62......73..[100](https://videos.openai.com/vg-assets/assets%2Ftask_01k1cjk105ebxsm93fv6x2nfae%2F1753842667_src_0.png?st=2025-07-30T01%3A08%3A06Z&se=2025-08-05T02%3A08%3A06Z&sks=b&skt=2025-07-30T01%3A08%3A06Z&ske=2025-08-05T02%3A08%3A06Z&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skoid=aa5ddad1-c91a-4f0a-9aca-e20682cc8969&skv=2019-02-02&sv=2018-11-09&sr=b&sp=r&spr=https%2Chttp&sig=SKyDvgjl8gBGyzBrP06C6oYYuJPm6VURGv0z6T%2FzOaU%3D&az=oaivgprodscus)\n\n> ✅ 生成完成\n\n\n![gen_01k1cjk2ftft4tavftj3td0wf9](https://filesystem.site/cdn/20250730/YSAYGijtdmRLQl81nT4O1ICFpomSVT.png)\n\n[点击下载](https://filesystem.site/cdn/download/20250730/YSAYGijtdmRLQl81nT4O1ICFpomSVT.png)"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 771,
    "completion_tokens": 1115,
    "total_tokens": 1886,
    "prompt_tokens_details": {
      "text_tokens": 764
    },
    "completion_tokens_details": {
      "content_tokens": 1115
    }
  }
}
=== API调用结束 [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2_api_0bc4a768] ===

2025-07-30 10:31:30,104 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] API调用成功，开始提取图片URL
2025-07-30 10:31:30,105 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] 提取到 6 个图片URL
2025-07-30 10:31:30,105 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] URL去重: 6 -> 4
2025-07-30 10:31:32,206 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] 图片MD5: a573deb6... - 保留
2025-07-30 10:31:35,498 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] 图片MD5: a573deb6... - 重复，跳过
2025-07-30 10:31:39,088 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] 图片MD5: a573deb6... - 重复，跳过
2025-07-30 10:31:40,254 - WARNING - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] 无法访问图片URL: https://videos.openai.com/vg-assets/assets%2Ftask_01k1cjk105ebxsm93fv6x2nfae%2F1753842667_src_0.png, 状态码: 400
2025-07-30 10:31:40,254 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] 内容去重: 4 -> 1
2025-07-30 10:31:40,255 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] 去重后剩余 1 个图片
2025-07-30 10:31:40,257 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_2] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\冰块遗漏分镜_分镜16_1753842700256.png
2025-07-30 10:31:40,258 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2] 批次 2 完成，生成 1 张图片
2025-07-30 10:32:16,113 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] API响应状态: 200, 耗时: 218.39秒
2025-07-30 10:32:16,114 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] 
=== API调用响应 [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1_api_e2ed2210] ===
状态码: 200
响应时间: 218.39秒
响应内容: {
  "id": "foaicmpl-84d1f10d-9cd0-4064-aadb-4a2372b19f4f",
  "model": "sora_image",
  "object": "chat.completion",
  "created": 1753842542,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/e728fc75-2850-4be2-ad21-4551d08fd18d.png)\n\n"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 1791,
    "completion_tokens": 45,
    "total_tokens": 1836,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "text_tokens": 0,
      "audio_tokens": 0,
      "image_tokens": 0
    },
    "completion_tokens_details": {
      "text_tokens": 0,
      "audio_tokens": 0,
      "reasoning_tokens": 0
    },
    "input_tokens": 0,
    "output_tokens": 0,
    "input_tokens_details": null
  }
}
=== API调用结束 [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1_api_e2ed2210] ===

2025-07-30 10:32:16,119 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] API调用成功，开始提取图片URL
2025-07-30 10:32:16,119 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] 提取到 1 个图片URL
2025-07-30 10:32:16,120 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] URL去重: 1 -> 1
2025-07-30 10:32:19,267 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] API响应状态: 200, 耗时: 221.54秒
2025-07-30 10:32:19,267 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] 
=== API调用响应 [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1_api_6f4c373a] ===
状态码: 200
响应时间: 221.54秒
响应内容: {
  "id": "foaicmpl-e95ca27c-0e45-4357-b38c-cc84dbf85a44",
  "model": "sora_image",
  "object": "chat.completion",
  "created": 1753842547,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/2a6b0210-eae5-484b-9eeb-9fd8f00605ea.png)\n\n![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/e436c535-48c1-45e5-af47-00175198d55c.png)\n\n"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 1558,
    "completion_tokens": 95,
    "total_tokens": 1653,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "text_tokens": 0,
      "audio_tokens": 0,
      "image_tokens": 0
    },
    "completion_tokens_details": {
      "text_tokens": 0,
      "audio_tokens": 0,
      "reasoning_tokens": 0
    },
    "input_tokens": 0,
    "output_tokens": 0,
    "input_tokens_details": null
  }
}
=== API调用结束 [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1_api_6f4c373a] ===

2025-07-30 10:32:19,271 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] API调用成功，开始提取图片URL
2025-07-30 10:32:19,271 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] 提取到 2 个图片URL
2025-07-30 10:32:19,272 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] URL去重: 2 -> 2
2025-07-30 10:32:19,280 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] 图片MD5: 55dba739... - 保留
2025-07-30 10:32:19,281 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] 内容去重: 1 -> 1
2025-07-30 10:32:19,282 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] 去重后剩余 1 个图片
2025-07-30 10:32:19,283 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2_batch_1] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\冰块遗漏分镜_分镜16_1753842739282.png
2025-07-30 10:32:19,284 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2] 批次 1 完成，生成 1 张图片
2025-07-30 10:32:19,284 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_2] 分镜生成完成，成功生成 2 张图片
2025-07-30 10:32:19,284 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f] 分镜 16 完成，生成 2 张图片
2025-07-30 10:32:22,282 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] 图片MD5: 638cae99... - 保留
2025-07-30 10:32:24,878 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] 图片MD5: 52198e41... - 保留
2025-07-30 10:32:24,879 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] 内容去重: 2 -> 2
2025-07-30 10:32:24,880 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] 去重后剩余 2 个图片
2025-07-30 10:32:24,882 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\冰块遗漏分镜_分镜15_1753842744880.png
2025-07-30 10:32:24,894 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1_batch_1] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\冰块遗漏分镜_分镜15_1753842744882.png
2025-07-30 10:32:24,896 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1] 批次 1 完成，生成 2 张图片
2025-07-30 10:32:24,897 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f_scene_1] 分镜生成完成，成功生成 3 张图片
2025-07-30 10:32:24,898 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f] 分镜 15 完成，生成 3 张图片
2025-07-30 10:32:24,898 - INFO - [5c98bd4b-2013-473f-b158-a7e8f5b6608f] 批量生成完成。总图片: 5, 成功分镜: 2, 失败分镜: 0
2025-07-30 10:32:24,898 - INFO - 127.0.0.1 - - [30/Jul/2025 10:32:24] "POST /api/generate/batch HTTP/1.1" 200 -
2025-07-30 10:39:09,133 - INFO - 127.0.0.1 - - [30/Jul/2025 10:39:09] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 10:39:09,141 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2] Starting batch generation for 2 scenes (total: 2)
2025-07-30 10:39:09,141 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2] 开始并发处理 2 个分镜，最大并发数: 2
2025-07-30 10:39:09,141 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1] 开始并发处理分镜 15
2025-07-30 10:39:09,141 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2] 开始并发处理分镜 16
2025-07-30 10:39:09,141 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1] 开始单个分镜生成: 15
2025-07-30 10:39:09,142 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2] 开始单个分镜生成: 16
2025-07-30 10:39:09,142 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1] 生成参数: service_id=1, model=sora_image, prompt='主体: Ballerina Cappuccina Baby; 细节: 穿着小恐龙连体衣的宝宝被完全封...', image_count=2
2025-07-30 10:39:09,142 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2] 生成参数: service_id=1, model=sora_image, prompt='主体:可爱Ballerina Cappuccina Baby; 细节: 宝宝被完全封在一个巨大的、正...', image_count=2
2025-07-30 10:39:09,142 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1] 开始并发生成 2 张图片，最大并发数: 2
2025-07-30 10:39:09,143 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2] 开始并发生成 2 张图片，最大并发数: 2
2025-07-30 10:39:09,143 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] 并发生成第 1/2 张图片
2025-07-30 10:39:09,143 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_2] 并发生成第 2/2 张图片
2025-07-30 10:39:09,143 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] API请求载荷构建完成
2025-07-30 10:39:09,143 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_2] API请求载荷构建完成
2025-07-30 10:39:09,143 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] 并发生成第 1/2 张图片
2025-07-30 10:39:09,144 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] 并发生成第 2/2 张图片
2025-07-30 10:39:09,144 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] API请求载荷构建完成
2025-07-30 10:39:09,144 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 10:39:09,144 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_2] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 10:39:09,145 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] API请求载荷构建完成
2025-07-30 10:39:09,145 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 10:39:09,145 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] 
=== API调用开始 [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1_api_e0d01a6e] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。主体: Ballerina Cappuccina Baby; 细节: 穿着小恐龙连体衣的宝宝被完全封在一个巨大的、正方体的透明冰块中，保持着蜷缩的姿势，双眼紧闭，脸上是萌萌哒的表情。冰块表面有气泡和冰裂纹; 环境: 冰箱抽屉内部的特写，清晰可见冰箱的白色塑料内壁; 时间: 中午; 天气: 极寒; 构图: 超近特写，着重表现宝宝被冰封的脆弱和可爱; 画风: 超逼真照片级，略带柔和的3D动画质感; 光影: 冰块反射着冰箱内部略显柔和的冷光，营造出晶莹剔透但又寒冷的环境; 参数: 极致的细节和清晰度，照片级真实感，细腻的皮肤和连体衣纹理，冰块和环境的材质都栩栩如生。自然的光影过渡，准确的色彩还原，无噪点，无失真，极致的8K分辨率。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 10:39:09,146 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_2] 
=== API调用开始 [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_2_api_4347934b] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。主体: Ballerina Cappuccina Baby; 细节: 穿着小恐龙连体衣的宝宝被完全封在一个巨大的、正方体的透明冰块中，保持着蜷缩的姿势，双眼紧闭，脸上是萌萌哒的表情。冰块表面有气泡和冰裂纹; 环境: 冰箱抽屉内部的特写，清晰可见冰箱的白色塑料内壁; 时间: 中午; 天气: 极寒; 构图: 超近特写，着重表现宝宝被冰封的脆弱和可爱; 画风: 超逼真照片级，略带柔和的3D动画质感; 光影: 冰块反射着冰箱内部略显柔和的冷光，营造出晶莹剔透但又寒冷的环境; 参数: 极致的细节和清晰度，照片级真实感，细腻的皮肤和连体衣纹理，冰块和环境的材质都栩栩如生。自然的光影过渡，准确的色彩还原，无噪点，无失真，极致的8K分辨率。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 10:39:09,146 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 10:39:09,147 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] 
=== API调用开始 [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1_api_ca88e21a] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\n3. 角色年龄是1~3岁的婴儿。\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体:可爱Ballerina Cappuccina Baby; 细节: 宝宝被完全封在一个巨大的、正方体的透明冰块中，身体蜷缩成一小块，双眼紧闭，脸上是萌萌哒超可爱的表情，例如微微嘟起的嘴唇和长长的、湿润的睫毛。冰块内部可见细小的气泡，表面有自然的冰裂纹; 环境: 冰箱抽屉内部的特写，清晰可见冰箱的白色塑料内壁; 时间: 中午; 天气: 极寒; 构图: 超近特写，着重表现宝宝被冰封的脆弱和可爱; 画风: 超逼真照片级，略带柔和的3D动画质感; 光影: 冰块反射着冰箱内部略显柔和的冷光，营造出晶莹剔透但又寒冷的环境; 参数: 极致的细节和清晰度，照片级真实感，细腻的皮肤和连体衣纹理，冰块和环境的材质都栩栩如生。自然的光影过渡，准确的色彩还原，无噪点，无失真，极致的8K分辨率。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 10:39:09,153 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] 
=== API调用开始 [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2_api_a5fa5ec7] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\n3. 角色年龄是1~3岁的婴儿。\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体:可爱Ballerina Cappuccina Baby; 细节: 宝宝被完全封在一个巨大的、正方体的透明冰块中，身体蜷缩成一小块，双眼紧闭，脸上是萌萌哒超可爱的表情，例如微微嘟起的嘴唇和长长的、湿润的睫毛。冰块内部可见细小的气泡，表面有自然的冰裂纹; 环境: 冰箱抽屉内部的特写，清晰可见冰箱的白色塑料内壁; 时间: 中午; 天气: 极寒; 构图: 超近特写，着重表现宝宝被冰封的脆弱和可爱; 画风: 超逼真照片级，略带柔和的3D动画质感; 光影: 冰块反射着冰箱内部略显柔和的冷光，营造出晶莹剔透但又寒冷的环境; 参数: 极致的细节和清晰度，照片级真实感，细腻的皮肤和连体衣纹理，冰块和环境的材质都栩栩如生。自然的光影过渡，准确的色彩还原，无噪点，无失真，极致的8K分辨率。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 10:41:41,003 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] API响应状态: 200, 耗时: 151.85秒
2025-07-30 10:41:41,004 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] 
=== API调用响应 [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2_api_a5fa5ec7] ===
状态码: 200
响应时间: 151.85秒
响应内容: {
  "id": "foaicmpl-b66369e1-3ee4-4bbb-b0fd-bc3901dd5a36",
  "model": "sora_image",
  "object": "chat.completion",
  "created": 1753843177,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/87cdccdc-bef1-4185-8ac1-ea06dfdc01e9.png)\n\n"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 1773,
    "completion_tokens": 47,
    "total_tokens": 1820,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "text_tokens": 0,
      "audio_tokens": 0,
      "image_tokens": 0
    },
    "completion_tokens_details": {
      "text_tokens": 0,
      "audio_tokens": 0,
      "reasoning_tokens": 0
    },
    "input_tokens": 0,
    "output_tokens": 0,
    "input_tokens_details": null
  }
}
=== API调用结束 [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2_api_a5fa5ec7] ===

2025-07-30 10:41:41,009 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] API调用成功，开始提取图片URL
2025-07-30 10:41:41,009 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] 提取到 1 个图片URL
2025-07-30 10:41:41,009 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] URL去重: 1 -> 1
2025-07-30 10:41:44,165 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] 图片MD5: 6eab8ca7... - 保留
2025-07-30 10:41:44,166 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] 内容去重: 1 -> 1
2025-07-30 10:41:44,167 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] 去重后剩余 1 个图片
2025-07-30 10:41:44,183 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_2] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\冰块遗漏分镜_分镜16_1753843304167.png
2025-07-30 10:41:44,184 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2] 批次 2 完成，生成 1 张图片
2025-07-30 10:41:52,988 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] API响应状态: 200, 耗时: 163.84秒
2025-07-30 10:41:52,988 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] 
=== API调用响应 [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1_api_e0d01a6e] ===
状态码: 200
响应时间: 163.84秒
响应内容: {
  "id": "foaicmpl-915d44a9-808f-49cf-9604-0e77c3e4e178",
  "model": "sora_image",
  "object": "chat.completion",
  "created": 1753843176,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/a2820a71-c909-48b0-9722-48310422d05c.png)\n\n"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 1562,
    "completion_tokens": 45,
    "total_tokens": 1607,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "text_tokens": 0,
      "audio_tokens": 0,
      "image_tokens": 0
    },
    "completion_tokens_details": {
      "text_tokens": 0,
      "audio_tokens": 0,
      "reasoning_tokens": 0
    },
    "input_tokens": 0,
    "output_tokens": 0,
    "input_tokens_details": null
  }
}
=== API调用结束 [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1_api_e0d01a6e] ===

2025-07-30 10:41:52,994 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] API调用成功，开始提取图片URL
2025-07-30 10:41:52,994 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] 提取到 1 个图片URL
2025-07-30 10:41:52,995 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] URL去重: 1 -> 1
2025-07-30 10:41:55,391 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] 图片MD5: 3f09d27c... - 保留
2025-07-30 10:41:55,392 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] 内容去重: 1 -> 1
2025-07-30 10:41:55,393 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] 去重后剩余 1 个图片
2025-07-30 10:41:55,395 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_1] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\冰块遗漏分镜_分镜15_1753843315393.png
2025-07-30 10:41:55,396 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1] 批次 1 完成，生成 1 张图片
2025-07-30 10:44:30,003 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] API响应状态: 200, 耗时: 320.86秒
2025-07-30 10:44:30,004 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] 
=== API调用响应 [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1_api_ca88e21a] ===
状态码: 200
响应时间: 320.86秒
响应内容: {
  "id": "chatcmpl-89DNcpZdHuxBotciRoX1ML6RCGner",
  "object": "chat.completion",
  "created": 1753843469,
  "model": "sora_image",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "```json\n{\n  \"prompt\": \"Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\\n3. 角色年龄是1~3岁的婴儿。\\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体:可爱Ballerina Cappuccina Baby; 细节: 宝宝被完全封在一个巨大的、正方体的透明冰块中，身体蜷缩成一小块，双眼紧闭，脸上是萌萌哒超可爱的表情，例如微微嘟起的嘴唇和长长的、湿润的睫毛。冰块内部可见细小的气泡，表面有自然的冰裂纹; 环境: 冰箱抽屉内部的特写，清晰可见冰箱的白色塑料内壁; 时间: 中午; 天气: 极寒; 构图: 超近特写，着重表现宝宝被冰封的脆弱和可爱; 画风: 超逼真照片级，略带柔和的3D动画质感; 光影: 冰块反射着冰箱内部略显柔和的冷光，营造出晶莹剔透但又寒冷的环境; 参数: 极致的细节和清晰度，照片级真实感，细腻的皮肤和连体衣纹理，冰块和环境的材质都栩栩如生。自然的光影过渡，准确的色彩还原，无噪点，无失真，极致的8K分辨率。\",\n  \"ratio\": \"9:16\",\n  \"n\": 1\n}\n```\n\n>🕐 排队中.\n\n>⚡ 生成中........\n\n>🏃‍ 进度 2.......8....12.....15.....20.....23.....27.....31.....35.....39.....43....46.....50.....55.....58.....62.....66....73...82..[100](https://videos.openai.com/vg-assets/assets%2Ftask_01k1ck7dk7e47r87p0s4fr0rt6%2F1753843427_src_0.png?st=2025-07-30T01%3A12%3A32Z&se=2025-08-05T02%3A12%3A32Z&sks=b&skt=2025-07-30T01%3A12%3A32Z&ske=2025-08-05T02%3A12%3A32Z&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skoid=8ebb0df1-a278-4e2e-9c20-f2d373479b3a&skv=2019-02-02&sv=2018-11-09&sr=b&sp=r&spr=https%2Chttp&sig=9guKcCw9rredDvWpnc6nDtcBVFWPqrAjn4qHMi8UtWM%3D&az=oaivgprodscus)\n\n> ✅ 生成完成\n\n\n![gen_01k1ck7f89edm8kzpnt8t4j8pz](https://filesystem.site/cdn/20250730/Dbwve5Muh0hxpMK1zmJsY54IQY5uvL.png)\n\n[点击下载](https://filesystem.site/cdn/download/20250730/Dbwve5Muh0hxpMK1zmJsY54IQY5uvL.png)"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 753,
    "completion_tokens": 1114,
    "total_tokens": 1867,
    "prompt_tokens_details": {
      "text_tokens": 746
    },
    "completion_tokens_details": {
      "content_tokens": 1114
    }
  }
}
=== API调用结束 [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1_api_ca88e21a] ===

2025-07-30 10:44:30,008 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] API调用成功，开始提取图片URL
2025-07-30 10:44:30,009 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] 提取到 6 个图片URL
2025-07-30 10:44:30,009 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] URL去重: 6 -> 4
2025-07-30 10:44:33,008 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] 图片MD5: da274d16... - 保留
2025-07-30 10:44:35,690 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] 图片MD5: da274d16... - 重复，跳过
2025-07-30 10:44:38,519 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] 图片MD5: da274d16... - 重复，跳过
2025-07-30 10:44:40,261 - WARNING - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] 无法访问图片URL: https://videos.openai.com/vg-assets/assets%2Ftask_01k1ck7dk7e47r87p0s4fr0rt6%2F1753843427_src_0.png, 状态码: 400
2025-07-30 10:44:40,261 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] 内容去重: 4 -> 1
2025-07-30 10:44:40,262 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] 去重后剩余 1 个图片
2025-07-30 10:44:40,264 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2_batch_1] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\冰块遗漏分镜_分镜16_1753843480263.png
2025-07-30 10:44:40,265 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2] 批次 1 完成，生成 1 张图片
2025-07-30 10:44:40,265 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_2] 分镜生成完成，成功生成 2 张图片
2025-07-30 10:44:40,266 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2] 分镜 16 完成，生成 2 张图片
2025-07-30 10:45:23,640 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_2] API响应状态: 200, 耗时: 374.49秒
2025-07-30 10:45:23,640 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_2] 
=== API调用响应 [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_2_api_4347934b] ===
状态码: 200
响应时间: 374.49秒
响应内容: {
  "id": "chatcmpl-89D2LW8QciHu9Zef85BQf0oTctPI6",
  "object": "chat.completion",
  "created": 1753843522,
  "model": "sora_image",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "```json\n{\n  \"prompt\": \"Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。主体: Ballerina Cappuccina Baby; 细节: 穿着小恐龙连体衣的宝宝被完全封在一个巨大的、正方体的透明冰块中，保持着蜷缩的姿势，双眼紧闭，脸上是萌萌哒的表情。冰块表面有气泡和冰裂纹; 环境: 冰箱抽屉内部的特写，清晰可见冰箱的白色塑料内壁; 时间: 中午; 天气: 极寒; 构图: 超近特写，着重表现宝宝被冰封的脆弱和可爱; 画风: 超逼真照片级，略带柔和的3D动画质感; 光影: 冰块反射着冰箱内部略显柔和的冷光，营造出晶莹剔透但又寒冷的环境; 参数: 极致的细节和清晰度，照片级真实感，细腻的皮肤和连体衣纹理，冰块和环境的材质都栩栩如生。自然的光影过渡，准确的色彩还原，无噪点，无失真，极致的8K分辨率。\",\n  \"ratio\": \"9:16\",\n  \"n\": 1\n}\n```\n\n>🕐 排队中.\n\n>⚡ 生成中......\n\n>🏃‍ 进度 3.....11.....20.....27....35.....43.....51.....59.....67....\n\n> 生成失败 ❌\n> 失败原因：output_moderation\n> **原因**: OpenAI 图片检测系统认为内容可能违反相关政策\n> **建议**: 请尝试修改提示词后重试\n\n```json\n{\"type\":\"terminal\",\"results_by_frame_index\":{},\"code\":null,\"is_output_rejection\":true,\"task_id\":\"task_01k1ckd4bcezatvktetqxz5q12\"}\n```"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 542,
    "completion_tokens": 630,
    "total_tokens": 1172,
    "prompt_tokens_details": {
      "text_tokens": 535
    },
    "completion_tokens_details": {
      "content_tokens": 630
    }
  }
}
=== API调用结束 [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_2_api_4347934b] ===

2025-07-30 10:45:23,643 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_2] API调用成功，开始提取图片URL
2025-07-30 10:45:23,644 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_2] 提取到 0 个图片URL
2025-07-30 10:45:23,644 - WARNING - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1_batch_2] 警告: 未从API响应中提取到图片URL
2025-07-30 10:45:23,644 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1] 批次 2 完成，生成 0 张图片
2025-07-30 10:45:23,644 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2_scene_1] 分镜生成完成，成功生成 1 张图片
2025-07-30 10:45:23,644 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2] 分镜 15 完成，生成 1 张图片
2025-07-30 10:45:23,645 - INFO - [6b3cbb79-4410-4823-b78d-6aecde3b57e2] 批量生成完成。总图片: 3, 成功分镜: 2, 失败分镜: 0
2025-07-30 10:45:23,645 - INFO - 127.0.0.1 - - [30/Jul/2025 10:45:23] "POST /api/generate/batch HTTP/1.1" 200 -
2025-07-30 10:51:45,302 - INFO - 127.0.0.1 - - [30/Jul/2025 10:51:45] "GET /api/config/export HTTP/1.1" 200 -
2025-07-30 11:21:10,426 - INFO - 127.0.0.1 - - [30/Jul/2025 11:21:10] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 11:21:11,145 - INFO - 127.0.0.1 - - [30/Jul/2025 11:21:11] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 11:21:22,735 - INFO - 127.0.0.1 - - [30/Jul/2025 11:21:22] "GET /api/imagehost/history HTTP/1.1" 200 -
2025-07-30 11:21:28,332 - INFO - 127.0.0.1 - - [30/Jul/2025 11:21:28] "GET /api/imagehost/config HTTP/1.1" 200 -
2025-07-30 11:21:28,332 - INFO - 127.0.0.1 - - [30/Jul/2025 11:21:28] "GET /api/imagehost/history HTTP/1.1" 200 -
2025-07-30 11:21:39,724 - INFO - Image uploaded successfully: https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250730_112137.png
2025-07-30 11:21:39,744 - INFO - Upload history saved: 机甲警察.png
2025-07-30 11:21:39,745 - INFO - 127.0.0.1 - - [30/Jul/2025 11:21:39] "POST /api/imagehost/upload HTTP/1.1" 200 -
2025-07-30 11:21:39,752 - INFO - 127.0.0.1 - - [30/Jul/2025 11:21:39] "GET /api/imagehost/history HTTP/1.1" 200 -
2025-07-30 11:21:46,498 - INFO - 127.0.0.1 - - [30/Jul/2025 11:21:46] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 11:21:55,766 - INFO - 127.0.0.1 - - [30/Jul/2025 11:21:55] "GET /api/imagehost/history HTTP/1.1" 200 -
2025-07-30 11:21:59,869 - INFO - 127.0.0.1 - - [30/Jul/2025 11:21:59] "POST /api/config/characters HTTP/1.1" 200 -
2025-07-30 11:21:59,874 - INFO - 127.0.0.1 - - [30/Jul/2025 11:21:59] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 14:18:03,414 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-07-30 14:18:03,418 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 14:18:03,420 - INFO -  * Restarting with stat
2025-07-30 14:18:04,119 - WARNING -  * Debugger is active!
2025-07-30 14:18:04,166 - INFO -  * Debugger PIN: 536-538-330
2025-07-30 14:18:07,429 - INFO - 127.0.0.1 - - [30/Jul/2025 14:18:07] "GET / HTTP/1.1" 200 -
2025-07-30 14:18:07,544 - INFO - 127.0.0.1 - - [30/Jul/2025 14:18:07] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-30 14:18:07,572 - INFO - 127.0.0.1 - - [30/Jul/2025 14:18:07] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-30 14:18:07,608 - INFO - 127.0.0.1 - - [30/Jul/2025 14:18:07] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 14:18:07,663 - INFO - 127.0.0.1 - - [30/Jul/2025 14:18:07] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 14:18:07,665 - INFO - 127.0.0.1 - - [30/Jul/2025 14:18:07] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 14:18:08,063 - INFO - 127.0.0.1 - - [30/Jul/2025 14:18:08] "GET /api/config/save-settings HTTP/1.1" 200 -
2025-07-30 14:26:47,534 - INFO - Excel file processed: 机甲医生.xlsx, 4 scenes
2025-07-30 14:26:47,535 - INFO - 127.0.0.1 - - [30/Jul/2025 14:26:47] "POST /api/excel/upload HTTP/1.1" 200 -
2025-07-30 14:26:54,247 - INFO - 127.0.0.1 - - [30/Jul/2025 14:26:54] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 14:27:03,721 - INFO - 127.0.0.1 - - [30/Jul/2025 14:27:03] "GET /api/imagehost/history HTTP/1.1" 200 -
2025-07-30 14:27:08,237 - INFO - 127.0.0.1 - - [30/Jul/2025 14:27:08] "GET /api/imagehost/history HTTP/1.1" 200 -
2025-07-30 14:27:11,299 - INFO - 127.0.0.1 - - [30/Jul/2025 14:27:11] "POST /api/config/characters HTTP/1.1" 200 -
2025-07-30 14:27:11,305 - INFO - 127.0.0.1 - - [30/Jul/2025 14:27:11] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 14:27:22,205 - INFO - 127.0.0.1 - - [30/Jul/2025 14:27:22] "GET /api/imagehost/history HTTP/1.1" 200 -
2025-07-30 14:27:22,219 - INFO - 127.0.0.1 - - [30/Jul/2025 14:27:22] "GET /api/imagehost/config HTTP/1.1" 200 -
2025-07-30 14:27:30,753 - INFO - Image uploaded successfully: https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250730_142728.png
2025-07-30 14:27:30,757 - INFO - Upload history saved: 高科技警车2.png
2025-07-30 14:27:30,758 - INFO - 127.0.0.1 - - [30/Jul/2025 14:27:30] "POST /api/imagehost/upload HTTP/1.1" 200 -
2025-07-30 14:27:30,766 - INFO - 127.0.0.1 - - [30/Jul/2025 14:27:30] "GET /api/imagehost/history HTTP/1.1" 200 -
2025-07-30 14:27:35,489 - INFO - 127.0.0.1 - - [30/Jul/2025 14:27:35] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 14:27:39,290 - INFO - 127.0.0.1 - - [30/Jul/2025 14:27:39] "GET /api/imagehost/history HTTP/1.1" 200 -
2025-07-30 14:27:44,856 - INFO - 127.0.0.1 - - [30/Jul/2025 14:27:44] "POST /api/config/characters HTTP/1.1" 200 -
2025-07-30 14:27:44,862 - INFO - 127.0.0.1 - - [30/Jul/2025 14:27:44] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 14:29:44,531 - INFO - 127.0.0.1 - - [30/Jul/2025 14:29:44] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 14:29:44,542 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e] Starting batch generation for 4 scenes (total: 4)
2025-07-30 14:29:44,543 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e] 开始并发处理 4 个分镜，最大并发数: 4
2025-07-30 14:29:44,543 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1] 开始并发处理分镜 17
2025-07-30 14:29:44,543 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1] 开始单个分镜生成: 17
2025-07-30 14:29:44,543 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2] 开始并发处理分镜 18
2025-07-30 14:29:44,544 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1] 生成参数: service_id=1, model=sora_image, prompt='主体: 两名机甲医生, Ballerina Cappuccina Baby; 细节: 医生是身穿白色...', image_count=2
2025-07-30 14:29:44,544 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3] 开始并发处理分镜 25
2025-07-30 14:29:44,544 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2] 开始单个分镜生成: 18
2025-07-30 14:29:44,544 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4] 开始并发处理分镜 26
2025-07-30 14:29:44,544 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1] 开始并发生成 2 张图片，最大并发数: 2
2025-07-30 14:29:44,544 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3] 开始单个分镜生成: 25
2025-07-30 14:29:44,545 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2] 生成参数: service_id=1, model=sora_image, prompt='主体: 三名机甲医生, Ballerina Cappuccina Baby; 细节: 冰块被放置在一...', image_count=2
2025-07-30 14:29:44,545 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4] 开始单个分镜生成: 26
2025-07-30 14:29:44,545 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] 并发生成第 1/2 张图片
2025-07-30 14:29:44,545 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3] 生成参数: service_id=1, model=sora_image, prompt='主体: 多辆高科技警车; 细节: 四五辆未来派的黑色装甲警车在高速公路上急速行驶。车身布满棱角分明的...', image_count=2
2025-07-30 14:29:44,545 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] 并发生成第 2/2 张图片
2025-07-30 14:29:44,546 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2] 开始并发生成 2 张图片，最大并发数: 2
2025-07-30 14:29:44,546 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4] 生成参数: service_id=1, model=sora_image, prompt='主体: Ballerina Cappuccina, 两名机甲警察; 细节: 穿着黑色短裙的妈妈，头部...', image_count=2
2025-07-30 14:29:44,546 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] API请求载荷构建完成
2025-07-30 14:29:44,546 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3] 开始并发生成 2 张图片，最大并发数: 2
2025-07-30 14:29:44,546 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] API请求载荷构建完成
2025-07-30 14:29:44,547 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] 并发生成第 1/2 张图片
2025-07-30 14:29:44,547 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4] 开始并发生成 2 张图片，最大并发数: 2
2025-07-30 14:29:44,547 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] 并发生成第 2/2 张图片
2025-07-30 14:29:44,548 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] 并发生成第 1/2 张图片
2025-07-30 14:29:44,548 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 14:29:44,548 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] API请求载荷构建完成
2025-07-30 14:29:44,549 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] 并发生成第 2/2 张图片
2025-07-30 14:29:44,549 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] 并发生成第 1/2 张图片
2025-07-30 14:29:44,550 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 14:29:44,550 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] API请求载荷构建完成
2025-07-30 14:29:44,550 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] 并发生成第 2/2 张图片
2025-07-30 14:29:44,550 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] API请求载荷构建完成
2025-07-30 14:29:44,551 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] 
=== API调用开始 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1_api_0e0d738b] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Iron Doctor的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第二张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\n3. 角色年龄是1~3岁的婴儿。\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体: 两名机甲医生, Ballerina Cappuccina Baby; 细节: 医生是身穿白色和灰色高科技装甲的机器人，背后有蓝色全息天使翅膀，脚底喷射出蓝色火焰，使他们悬浮在地面上，他们正极速推动着一辆医院担架车。车上平放着那个冰封着小恐龙连体衣宝宝的巨大冰块。机甲医生的头盔表情严肃; 环境: 未来感十足的、明亮狭长的医院走廊，墙壁是金属白色，天花板上有一排排的LED灯带; 时间: 下午; 天气: 室内; 构图: 中景，镜头位于担架车前方并向后移动，营造出极速的紧急感; 画风: 照片级真实感，科幻电影概念艺术风格; 光影: 走廊的LED灯光和脚部喷射的蓝色火焰是主要光源，光线冰冷，充满科技感; 参数: 画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_220902.png"
          }
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 14:29:44,551 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] API请求载荷构建完成
2025-07-30 14:29:44,552 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 14:29:44,552 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] API请求载荷构建完成
2025-07-30 14:29:44,552 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] 
=== API调用开始 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2_api_72b98bbd] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Iron Doctor的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第二张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\n3. 角色年龄是1~3岁的婴儿。\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体: 两名机甲医生, Ballerina Cappuccina Baby; 细节: 医生是身穿白色和灰色高科技装甲的机器人，背后有蓝色全息天使翅膀，脚底喷射出蓝色火焰，使他们悬浮在地面上，他们正极速推动着一辆医院担架车。车上平放着那个冰封着小恐龙连体衣宝宝的巨大冰块。机甲医生的头盔表情严肃; 环境: 未来感十足的、明亮狭长的医院走廊，墙壁是金属白色，天花板上有一排排的LED灯带; 时间: 下午; 天气: 室内; 构图: 中景，镜头位于担架车前方并向后移动，营造出极速的紧急感; 画风: 照片级真实感，科幻电影概念艺术风格; 光影: 走廊的LED灯光和脚部喷射的蓝色火焰是主要光源，光线冰冷，充满科技感; 参数: 画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_220902.png"
          }
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 14:29:44,553 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] API请求载荷构建完成
2025-07-30 14:29:44,553 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 14:29:44,553 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 14:29:44,557 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] 
=== API调用开始 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1_api_197f6560] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Iron Doctor的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第二张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\n3. 角色年龄是1~3岁的婴儿。\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体: 三名机甲医生, Ballerina Cappuccina Baby; 细节: 冰块被放置在一个高科技手术台上，周围是融化的水和碎冰。三名机甲医生围绕着手术台，身体前倾，从他们头盔的眉心处发射出精确的、高热的橙色能量光束，共同聚焦在冰块上使其迅速融化。可以隐约看到冰块里的小恐龙连体衣; 环境: 充满未来感的科幻手术室，背景是各种闪烁着数据的全息显示屏和高科技医疗设备; 时间: 下午; 天气: 室内; 构图: 俯瞰特写，聚焦于三道光束汇聚融化冰块的过程; 画风: 照片级真实感，科幻电影概念艺术风格; 光影: 能量光束发出的炽热橙色光芒是主要光源，与手术室的蓝色冷色调环境光形成强烈对比; 参数: 画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_220902.png"
          }
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 14:29:44,557 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 14:29:44,558 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 14:29:44,561 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] 
=== API调用开始 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2_api_fb343e4f] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Iron Doctor的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第二张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\n3. 角色年龄是1~3岁的婴儿。\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体: 三名机甲医生, Ballerina Cappuccina Baby; 细节: 冰块被放置在一个高科技手术台上，周围是融化的水和碎冰。三名机甲医生围绕着手术台，身体前倾，从他们头盔的眉心处发射出精确的、高热的橙色能量光束，共同聚焦在冰块上使其迅速融化。可以隐约看到冰块里的小恐龙连体衣; 环境: 充满未来感的科幻手术室，背景是各种闪烁着数据的全息显示屏和高科技医疗设备; 时间: 下午; 天气: 室内; 构图: 俯瞰特写，聚焦于三道光束汇聚融化冰块的过程; 画风: 照片级真实感，科幻电影概念艺术风格; 光影: 能量光束发出的炽热橙色光芒是主要光源，与手术室的蓝色冷色调环境光形成强烈对比; 参数: 画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_220902.png"
          }
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250729_094407.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 14:29:44,561 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] 开始API调用: https://yunwu.ai/v1/chat/completions
2025-07-30 14:29:44,561 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] 
=== API调用开始 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1_api_e50254db] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Iron Doctor的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。主体: 多辆高科技警车; 细节: 四五辆未来派的黑色装甲警车在高速公路上急速行驶。车身布满棱角分明的装甲板，车顶装有重型机枪炮塔和蓝色的LED警灯条，车头有发光的“POLICE”字样和蓝色环形灯。车轮卷起大量烟尘，形成一条极具压迫感的追逐车队; 环境: 未来城市的宽阔高速公路，两侧是带有霓虹灯光的高楼大厦和飞行车道; 时间: 黄昏，赛博朋克风格的夜晚; 天气: 夜晚，路面有轻微反光; 构图: 远景，动态追踪拍摄，充满速度感和紧迫感; 画风: 照片级真实感，科幻电影概念艺术，赛博朋克风格; 光影: 警车的蓝色光芒和城市背景的霓虹灯在昏暗的环境中划出长长的光轨; 参数: 画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250730_142728.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 14:29:44,564 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] 
=== API调用开始 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2_api_ddcacd90] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Iron Doctor的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。主体: 多辆高科技警车; 细节: 四五辆未来派的黑色装甲警车在高速公路上急速行驶。车身布满棱角分明的装甲板，车顶装有重型机枪炮塔和蓝色的LED警灯条，车头有发光的“POLICE”字样和蓝色环形灯。车轮卷起大量烟尘，形成一条极具压迫感的追逐车队; 环境: 未来城市的宽阔高速公路，两侧是带有霓虹灯光的高楼大厦和飞行车道; 时间: 黄昏，赛博朋克风格的夜晚; 天气: 夜晚，路面有轻微反光; 构图: 远景，动态追踪拍摄，充满速度感和紧迫感; 画风: 照片级真实感，科幻电影概念艺术，赛博朋克风格; 光影: 警车的蓝色光芒和城市背景的霓虹灯在昏暗的环境中划出长长的光轨; 参数: 画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250730_142728.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 14:29:44,564 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] 
=== API调用开始 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1_api_205935cf] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Iron Doctor的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。Ballerina Cappuccina的角色形象:请严格参考我提供的第二张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\n3. 芭蕾舞者般的身形，优雅修长，腰肢纤细，曲线玲珑有致。\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体: Ballerina Cappuccina, 两名机甲警察; 细节: 穿着黑色短裙的妈妈，头部杯子布满裂痕，表情绝望地哭泣，她的双臂被一左一右两名身穿黑色高科技战术装甲的机甲警察架着。机甲警察头盔上有蓝色V形发光眼罩和金色警徽，胸前有发光的“POLICE”字样，他们正在一条街道上往前走; 环境: 未来城市的街道，背景有充满科技感的建筑物和悬浮车辆; 时间: 白天; 天气: 晴朗; 构图: 中景，镜头跟随他们移动，展示被逮捕的最终结局; 画风: 照片级真实感，科幻电影概念艺术风格; 光影: 明亮的日光，机甲警察身上的蓝色光芒在日光下依然清晰可见，有一种尘埃落定的感觉; 参数: 画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250730_112137.png"
          }
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250728_112948.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 14:29:44,567 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] 
=== API调用开始 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2_api_e66d8951] ===
URL: https://yunwu.ai/v1/chat/completions
请求头: {
  "Content-Type": "application/json",
  "Authorization": "Bearer sk-MLN6HLwqkFDUjK8yyJmIOmV9LrczAfkacZaxf7GwYMTgUGiY"
}
请求载荷: {
  "model": "sora_image",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": "Iron Doctor的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。Ballerina Cappuccina的角色形象:请严格参考我提供的第二张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\n3. 芭蕾舞者般的身形，优雅修长，腰肢纤细，曲线玲珑有致。\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体: Ballerina Cappuccina, 两名机甲警察; 细节: 穿着黑色短裙的妈妈，头部杯子布满裂痕，表情绝望地哭泣，她的双臂被一左一右两名身穿黑色高科技战术装甲的机甲警察架着。机甲警察头盔上有蓝色V形发光眼罩和金色警徽，胸前有发光的“POLICE”字样，他们正在一条街道上往前走; 环境: 未来城市的街道，背景有充满科技感的建筑物和悬浮车辆; 时间: 白天; 天气: 晴朗; 构图: 中景，镜头跟随他们移动，展示被逮捕的最终结局; 画风: 照片级真实感，科幻电影概念艺术风格; 光影: 明亮的日光，机甲警察身上的蓝色光芒在日光下依然清晰可见，有一种尘埃落定的感觉; 参数: 画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。--ar 9:16"
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250730_112137.png"
          }
        },
        {
          "type": "image_url",
          "image_url": {
            "url": "https://raw.githubusercontent.com/scys-wcs/photo/main/image/20250728_112948.png"
          }
        }
      ]
    }
  ],
  "stream": false
}

2025-07-30 14:32:00,102 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] API响应状态: 200, 耗时: 135.53秒
2025-07-30 14:32:00,102 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] 
=== API调用响应 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2_api_e66d8951] ===
状态码: 200
响应时间: 135.53秒
响应内容: {
  "id": "foaicmpl-50b46e0c-775a-47c8-9c02-ca4979e3d5c8",
  "model": "sora_image",
  "object": "chat.completion",
  "created": 1753856996,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/607e87f1-1bc9-44be-ac29-f0edd281be51.png)\n\n"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 2984,
    "completion_tokens": 45,
    "total_tokens": 3029,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "text_tokens": 0,
      "audio_tokens": 0,
      "image_tokens": 0
    },
    "completion_tokens_details": {
      "text_tokens": 0,
      "audio_tokens": 0,
      "reasoning_tokens": 0
    },
    "input_tokens": 0,
    "output_tokens": 0,
    "input_tokens_details": null
  }
}
=== API调用结束 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2_api_e66d8951] ===

2025-07-30 14:32:00,105 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] API调用成功，开始提取图片URL
2025-07-30 14:32:00,106 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] 提取到 1 个图片URL
2025-07-30 14:32:00,106 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] URL去重: 1 -> 1
2025-07-30 14:32:02,330 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] 图片MD5: 96f66ca2... - 保留
2025-07-30 14:32:02,330 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] 内容去重: 1 -> 1
2025-07-30 14:32:02,331 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] 去重后剩余 1 个图片
2025-07-30 14:32:02,334 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_2] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\机甲医生_分镜26_1753857122331.png
2025-07-30 14:32:02,335 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4] 批次 2 完成，生成 1 张图片
2025-07-30 14:32:10,017 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] API响应状态: 200, 耗时: 145.46秒
2025-07-30 14:32:10,017 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] 
=== API调用响应 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2_api_fb343e4f] ===
状态码: 200
响应时间: 145.46秒
响应内容: {
  "id": "foaicmpl-998c769e-1795-40a2-8516-e8ef183a8382",
  "model": "sora_image",
  "object": "chat.completion",
  "created": 1753856998,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/2816f3f2-3edb-43b0-957e-3e7cae281ee5.png)\n\n![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/0d1bd243-bbfb-49fd-851e-9f52071c69bc.png)\n\n"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 2969,
    "completion_tokens": 96,
    "total_tokens": 3065,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "text_tokens": 0,
      "audio_tokens": 0,
      "image_tokens": 0
    },
    "completion_tokens_details": {
      "text_tokens": 0,
      "audio_tokens": 0,
      "reasoning_tokens": 0
    },
    "input_tokens": 0,
    "output_tokens": 0,
    "input_tokens_details": null
  }
}
=== API调用结束 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2_api_fb343e4f] ===

2025-07-30 14:32:10,023 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] API调用成功，开始提取图片URL
2025-07-30 14:32:10,023 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] 提取到 2 个图片URL
2025-07-30 14:32:10,023 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] URL去重: 2 -> 2
2025-07-30 14:32:10,832 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] API响应状态: 200, 耗时: 146.28秒
2025-07-30 14:32:10,832 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] 
=== API调用响应 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1_api_0e0d738b] ===
状态码: 200
响应时间: 146.28秒
响应内容: {
  "id": "foaicmpl-2d152b88-4823-4984-9b30-9b3afadcbc40",
  "model": "sora_image",
  "object": "chat.completion",
  "created": 1753857001,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/e088dbe4-298b-435d-853d-2c9cf51ccd1e.png)\n\n"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 2975,
    "completion_tokens": 46,
    "total_tokens": 3021,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "text_tokens": 0,
      "audio_tokens": 0,
      "image_tokens": 0
    },
    "completion_tokens_details": {
      "text_tokens": 0,
      "audio_tokens": 0,
      "reasoning_tokens": 0
    },
    "input_tokens": 0,
    "output_tokens": 0,
    "input_tokens_details": null
  }
}
=== API调用结束 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1_api_0e0d738b] ===

2025-07-30 14:32:10,837 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] API调用成功，开始提取图片URL
2025-07-30 14:32:10,837 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] 提取到 1 个图片URL
2025-07-30 14:32:10,837 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] URL去重: 1 -> 1
2025-07-30 14:32:13,403 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] 图片MD5: acf5748b... - 保留
2025-07-30 14:32:13,403 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] 内容去重: 1 -> 1
2025-07-30 14:32:13,404 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] 去重后剩余 1 个图片
2025-07-30 14:32:13,417 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_1] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\机甲医生_分镜17_1753857133405.png
2025-07-30 14:32:13,418 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1] 批次 1 完成，生成 1 张图片
2025-07-30 14:32:16,042 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] API响应状态: 200, 耗时: 151.48秒
2025-07-30 14:32:16,042 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] 
=== API调用响应 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1_api_205935cf] ===
状态码: 200
响应时间: 151.48秒
响应内容: {
  "id": "foaicmpl-1157f1b5-ed14-4b00-837a-f538b3b7d321",
  "model": "sora_image",
  "object": "chat.completion",
  "created": 1753857007,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/2f70824b-f3a4-4d7d-8e19-5f2315146f5f.png)\n\n![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/95f522af-00ed-43b0-9def-02f02a6d247a.png)\n\n"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 2984,
    "completion_tokens": 100,
    "total_tokens": 3084,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "text_tokens": 0,
      "audio_tokens": 0,
      "image_tokens": 0
    },
    "completion_tokens_details": {
      "text_tokens": 0,
      "audio_tokens": 0,
      "reasoning_tokens": 0
    },
    "input_tokens": 0,
    "output_tokens": 0,
    "input_tokens_details": null
  }
}
=== API调用结束 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1_api_205935cf] ===

2025-07-30 14:32:16,046 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] API调用成功，开始提取图片URL
2025-07-30 14:32:16,046 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] 提取到 2 个图片URL
2025-07-30 14:32:16,046 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] URL去重: 2 -> 2
2025-07-30 14:32:19,108 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] 图片MD5: 3b45ea2f... - 保留
2025-07-30 14:32:21,105 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] 图片MD5: 9acaf0c3... - 保留
2025-07-30 14:32:24,411 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] 图片MD5: d65f7707... - 保留
2025-07-30 14:32:24,412 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] 内容去重: 2 -> 2
2025-07-30 14:32:24,413 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] 去重后剩余 2 个图片
2025-07-30 14:32:24,426 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\机甲医生_分镜26_1753857144413.png
2025-07-30 14:32:24,428 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4_batch_1] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\机甲医生_分镜26_1753857144427.png
2025-07-30 14:32:24,429 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4] 批次 1 完成，生成 2 张图片
2025-07-30 14:32:24,429 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_4] 分镜生成完成，成功生成 3 张图片
2025-07-30 14:32:24,430 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e] 分镜 26 完成，生成 3 张图片
2025-07-30 14:32:25,302 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] 图片MD5: e6c6f907... - 保留
2025-07-30 14:32:25,302 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] 内容去重: 2 -> 2
2025-07-30 14:32:25,303 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] 去重后剩余 2 个图片
2025-07-30 14:32:25,305 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\机甲医生_分镜18_1753857145304.png
2025-07-30 14:32:25,307 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_2] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\机甲医生_分镜18_1753857145306.png
2025-07-30 14:32:25,308 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2] 批次 2 完成，生成 2 张图片
2025-07-30 14:33:12,177 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] API响应状态: 200, 耗时: 207.61秒
2025-07-30 14:33:12,177 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] 
=== API调用响应 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2_api_ddcacd90] ===
状态码: 200
响应时间: 207.61秒
响应内容: {
  "id": "foaicmpl-99048a29-aae2-492f-97fa-9e5116b9c6ba",
  "model": "sora_image",
  "object": "chat.completion",
  "created": 1753856996,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/1cc7f3a2-21e4-49a7-8616-731158aeebec.png)\n\n"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 1302,
    "completion_tokens": 49,
    "total_tokens": 1351,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "text_tokens": 0,
      "audio_tokens": 0,
      "image_tokens": 0
    },
    "completion_tokens_details": {
      "text_tokens": 0,
      "audio_tokens": 0,
      "reasoning_tokens": 0
    },
    "input_tokens": 0,
    "output_tokens": 0,
    "input_tokens_details": null
  }
}
=== API调用结束 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2_api_ddcacd90] ===

2025-07-30 14:33:12,183 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] API调用成功，开始提取图片URL
2025-07-30 14:33:12,184 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] 提取到 1 个图片URL
2025-07-30 14:33:12,184 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] URL去重: 1 -> 1
2025-07-30 14:33:14,485 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] 图片MD5: ab51572d... - 保留
2025-07-30 14:33:14,485 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] 内容去重: 1 -> 1
2025-07-30 14:33:14,486 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] 去重后剩余 1 个图片
2025-07-30 14:33:14,488 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_2] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\机甲医生_分镜25_1753857194487.png
2025-07-30 14:33:14,489 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3] 批次 2 完成，生成 1 张图片
2025-07-30 14:33:15,039 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] API响应状态: 200, 耗时: 210.48秒
2025-07-30 14:33:15,039 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] 
=== API调用响应 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1_api_e50254db] ===
状态码: 200
响应时间: 210.48秒
响应内容: {
  "id": "foaicmpl-37dbb56a-0a15-4d24-9012-33f3c96f1897",
  "model": "sora_image",
  "object": "chat.completion",
  "created": 1753856994,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/16ab231a-08b9-4290-a8f8-695a84adcc5a.png)\n\n![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/891b248a-5b84-4bcc-bfcf-7e31cbf0bd7a.png)\n\n"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 1302,
    "completion_tokens": 97,
    "total_tokens": 1399,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "text_tokens": 0,
      "audio_tokens": 0,
      "image_tokens": 0
    },
    "completion_tokens_details": {
      "text_tokens": 0,
      "audio_tokens": 0,
      "reasoning_tokens": 0
    },
    "input_tokens": 0,
    "output_tokens": 0,
    "input_tokens_details": null
  }
}
=== API调用结束 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1_api_e50254db] ===

2025-07-30 14:33:15,043 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] API调用成功，开始提取图片URL
2025-07-30 14:33:15,044 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] 提取到 2 个图片URL
2025-07-30 14:33:15,044 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] URL去重: 2 -> 2
2025-07-30 14:33:18,430 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] 图片MD5: 942917ae... - 保留
2025-07-30 14:33:24,704 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] 图片MD5: 686befc4... - 保留
2025-07-30 14:33:24,704 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] 内容去重: 2 -> 2
2025-07-30 14:33:24,705 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] 去重后剩余 2 个图片
2025-07-30 14:33:24,707 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\机甲医生_分镜25_1753857204705.png
2025-07-30 14:33:24,708 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3_batch_1] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\机甲医生_分镜25_1753857204707.png
2025-07-30 14:33:24,709 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3] 批次 1 完成，生成 2 张图片
2025-07-30 14:33:24,709 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_3] 分镜生成完成，成功生成 3 张图片
2025-07-30 14:33:24,709 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e] 分镜 25 完成，生成 3 张图片
2025-07-30 14:33:40,492 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] API响应状态: 200, 耗时: 235.94秒
2025-07-30 14:33:40,493 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] 
=== API调用响应 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1_api_197f6560] ===
状态码: 200
响应时间: 235.94秒
响应内容: {
  "id": "foaicmpl-2ffe962c-4fa3-45af-94fe-fa2daaa4231e",
  "model": "sora_image",
  "object": "chat.completion",
  "created": 1753856996,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/702d37b1-7f42-4c74-96a7-5ce220a5fca8.png)\n\n![图片](https://midjourney-plus.oss-us-west-1.aliyuncs.com/sora/712041be-f072-45f4-bcee-5a26751d729f.png)\n\n"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 2969,
    "completion_tokens": 95,
    "total_tokens": 3064,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "text_tokens": 0,
      "audio_tokens": 0,
      "image_tokens": 0
    },
    "completion_tokens_details": {
      "text_tokens": 0,
      "audio_tokens": 0,
      "reasoning_tokens": 0
    },
    "input_tokens": 0,
    "output_tokens": 0,
    "input_tokens_details": null
  }
}
=== API调用结束 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1_api_197f6560] ===

2025-07-30 14:33:40,497 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] API调用成功，开始提取图片URL
2025-07-30 14:33:40,497 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] 提取到 2 个图片URL
2025-07-30 14:33:40,497 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] URL去重: 2 -> 2
2025-07-30 14:33:42,096 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] API响应状态: 200, 耗时: 237.54秒
2025-07-30 14:33:42,097 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] 
=== API调用响应 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2_api_72b98bbd] ===
状态码: 200
响应时间: 237.54秒
响应内容: {
  "id": "chatcmpl-89DFDIBSEO0HhnUvabkSY6hXlPWKW",
  "object": "chat.completion",
  "created": 1753857221,
  "model": "sora_image",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "```json\n{\n  \"prompt\": \"Iron Doctor的角色形象:请严格参考我提供的第一张图片(角色参考图)来塑造角色。Ballerina Cappuccina Baby的角色形象:请严格参考我提供的第二张图片(角色参考图)来塑造角色。角色不可变的核心特征=【1. 她的头是一个陶瓷卡布奇诺杯，杯口有清晰的拉花图案。\\n2. 在杯子的正面，长出了完整的人类五官（眼睛、鼻子、嘴巴），自然融合在杯体中。\\n3. 角色年龄是1~3岁的婴儿。\\n4. 无论换什么衣服、场景或姿势，她的结构必须始终是：“咖啡杯头 + 杯面拉花 + 杯体长人脸”。】主体: 两名机甲医生, Ballerina Cappuccina Baby; 细节: 医生是身穿白色和灰色高科技装甲的机器人，背后有蓝色全息天使翅膀，脚底喷射出蓝色火焰，使他们悬浮在地面上，他们正极速推动着一辆医院担架车。车上平放着那个冰封着小恐龙连体衣宝宝的巨大冰块。机甲医生的头盔表情严肃; 环境: 未来感十足的、明亮狭长的医院走廊，墙壁是金属白色，天花板上有一排排的LED灯带; 时间: 下午; 天气: 室内; 构图: 中景，镜头位于担架车前方并向后移动，营造出极速的紧急感; 画风: 照片级真实感，科幻电影概念艺术风格; 光影: 走廊的LED灯光和脚部喷射的蓝色火焰是主要光源，光线冰冷，充满科技感; 参数: 画面高度细腻，细节极其丰富，达到照片级真实感。追求极致的清晰度和纹理表现，所有物体的材质质感都应逼真呈现。光影过渡自然平滑，色彩还原准确，无噪点，无失真，无数字感。8K分辨率视觉效果。\",\n  \"ratio\": \"9:16\",\n  \"n\": 1\n}\n```\n\n>🕐 排队中.\n\n>⚡ 生成中........\n\n>🏃‍ 进度 1.....6.....11.......18....22.....27.......34.....39.....43.....48....52.....57....62.....66......[100](https://videos.openai.com/vg-assets/assets%2Ftask_01k1d0d2f2ec8tm6ey8v3yx0nt%2F1753857217_src_0.png?st=2025-07-30T05%3A05%3A33Z&se=2025-08-05T06%3A05%3A33Z&sks=b&skt=2025-07-30T05%3A05%3A33Z&ske=2025-08-05T06%3A05%3A33Z&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skoid=3d249c53-07fa-4ba4-9b65-0bf8eb4ea46a&skv=2019-02-02&sv=2018-11-09&sr=b&sp=r&spr=https%2Chttp&sig=snmUXPjBVC3tcA2eg%2FUZcv34nKHs4kYDvVu85AwNVPU%3D&az=oaivgprodscus)\n\n> ✅ 生成完成\n\n\n![gen_01k1d0d4hjeaha1365ybn4xj51](https://filesystem.site/cdn/20250730/Jm8eebhsZESPtPfgZGs2S0bgunDe0S.png)\n\n[点击下载](https://filesystem.site/cdn/download/20250730/Jm8eebhsZESPtPfgZGs2S0bgunDe0S.png)"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 935,
    "completion_tokens": 1203,
    "total_tokens": 2138,
    "prompt_tokens_details": {
      "text_tokens": 928
    },
    "completion_tokens_d...
=== API调用结束 [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2_api_72b98bbd] ===

2025-07-30 14:33:42,102 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] API调用成功，开始提取图片URL
2025-07-30 14:33:42,102 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] 提取到 6 个图片URL
2025-07-30 14:33:42,102 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] URL去重: 6 -> 4
2025-07-30 14:33:42,645 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] 图片MD5: 459ef398... - 保留
2025-07-30 14:33:44,234 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] 图片MD5: 62aac354... - 保留
2025-07-30 14:33:55,639 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] 图片MD5: 62aac354... - 重复，跳过
2025-07-30 14:33:57,747 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] 图片MD5: 62aac354... - 重复，跳过
2025-07-30 14:33:58,702 - WARNING - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] 无法访问图片URL: https://videos.openai.com/vg-assets/assets%2Ftask_01k1d0d2f2ec8tm6ey8v3yx0nt%2F1753857217_src_0.png, 状态码: 400
2025-07-30 14:33:58,703 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] 内容去重: 4 -> 1
2025-07-30 14:33:58,704 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] 去重后剩余 1 个图片
2025-07-30 14:33:58,706 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1_batch_2] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\机甲医生_分镜17_1753857238705.png
2025-07-30 14:33:58,707 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1] 批次 2 完成，生成 1 张图片
2025-07-30 14:33:58,707 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_1] 分镜生成完成，成功生成 2 张图片
2025-07-30 14:33:58,707 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e] 分镜 17 完成，生成 2 张图片
2025-07-30 14:34:01,444 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] 图片MD5: 2514de13... - 保留
2025-07-30 14:34:01,444 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] 内容去重: 2 -> 2
2025-07-30 14:34:01,446 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] 去重后剩余 2 个图片
2025-07-30 14:34:01,452 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\机甲医生_分镜18_1753857241446.png
2025-07-30 14:34:01,453 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2_batch_1] 图片保存成功: D:\\BaiduSyncdisk\\Youtube\\wcs_util\机甲医生_分镜18_1753857241452.png
2025-07-30 14:34:01,454 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2] 批次 1 完成，生成 2 张图片
2025-07-30 14:34:01,455 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e_scene_2] 分镜生成完成，成功生成 4 张图片
2025-07-30 14:34:01,455 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e] 分镜 18 完成，生成 4 张图片
2025-07-30 14:34:01,455 - INFO - [5f8df88a-c41e-4e74-a685-a3f8b3cd916e] 批量生成完成。总图片: 12, 成功分镜: 4, 失败分镜: 0
2025-07-30 14:34:01,456 - INFO - 127.0.0.1 - - [30/Jul/2025 14:34:01] "POST /api/generate/batch HTTP/1.1" 200 -
2025-07-30 14:42:03,007 - INFO - 127.0.0.1 - - [30/Jul/2025 14:42:03] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 14:42:03,011 - INFO - 127.0.0.1 - - [30/Jul/2025 14:42:03] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 14:42:04,103 - INFO - 127.0.0.1 - - [30/Jul/2025 14:42:04] "GET /api/imagehost/config HTTP/1.1" 200 -
2025-07-30 14:42:04,105 - INFO - 127.0.0.1 - - [30/Jul/2025 14:42:04] "GET /api/imagehost/history HTTP/1.1" 200 -
2025-07-30 14:42:04,694 - INFO - 127.0.0.1 - - [30/Jul/2025 14:42:04] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 14:59:03,066 - INFO - 127.0.0.1 - - [30/Jul/2025 14:59:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-30 15:00:32,971 - INFO - 127.0.0.1 - - [30/Jul/2025 15:00:32] "GET / HTTP/1.1" 200 -
2025-07-30 15:00:33,006 - INFO - 127.0.0.1 - - [30/Jul/2025 15:00:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-30 15:00:33,007 - INFO - 127.0.0.1 - - [30/Jul/2025 15:00:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-30 15:00:33,065 - INFO - 127.0.0.1 - - [30/Jul/2025 15:00:33] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 15:00:33,460 - INFO - 127.0.0.1 - - [30/Jul/2025 15:00:33] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 15:00:33,461 - INFO - 127.0.0.1 - - [30/Jul/2025 15:00:33] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 15:00:33,498 - INFO - 127.0.0.1 - - [30/Jul/2025 15:00:33] "GET /api/config/save-settings HTTP/1.1" 200 -
2025-07-30 15:00:40,859 - INFO - 127.0.0.1 - - [30/Jul/2025 15:00:40] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 15:00:49,807 - INFO - 127.0.0.1 - - [30/Jul/2025 15:00:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-30 15:02:45,054 - INFO - 127.0.0.1 - - [30/Jul/2025 15:02:45] "GET / HTTP/1.1" 200 -
2025-07-30 15:02:45,101 - INFO - 127.0.0.1 - - [30/Jul/2025 15:02:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-30 15:02:45,104 - INFO - 127.0.0.1 - - [30/Jul/2025 15:02:45] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-30 15:02:45,191 - INFO - 127.0.0.1 - - [30/Jul/2025 15:02:45] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 15:02:45,664 - INFO - 127.0.0.1 - - [30/Jul/2025 15:02:45] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 15:02:45,665 - INFO - 127.0.0.1 - - [30/Jul/2025 15:02:45] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 15:02:45,781 - INFO - 127.0.0.1 - - [30/Jul/2025 15:02:45] "GET /api/config/save-settings HTTP/1.1" 200 -
2025-07-30 15:02:46,879 - INFO - 127.0.0.1 - - [30/Jul/2025 15:02:46] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 15:03:03,151 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:03] "GET / HTTP/1.1" 200 -
2025-07-30 15:03:03,212 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-30 15:03:03,219 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:03] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-30 15:03:03,278 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:03] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 15:03:03,685 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:03] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 15:03:03,687 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:03] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 15:03:03,863 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:03] "GET /api/config/save-settings HTTP/1.1" 200 -
2025-07-30 15:03:03,993 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:03] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 15:03:56,577 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:56] "GET / HTTP/1.1" 200 -
2025-07-30 15:03:56,614 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-30 15:03:56,628 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:56] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-30 15:03:56,714 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:56] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 15:03:57,180 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:57] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 15:03:57,180 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:57] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 15:03:57,241 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:57] "GET /api/config/save-settings HTTP/1.1" 200 -
2025-07-30 15:03:58,088 - INFO - 127.0.0.1 - - [30/Jul/2025 15:03:58] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 15:10:07,767 - INFO - 127.0.0.1 - - [30/Jul/2025 15:10:07] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 15:10:07,779 - INFO - 127.0.0.1 - - [30/Jul/2025 15:10:07] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 15:10:08,298 - INFO - 127.0.0.1 - - [30/Jul/2025 15:10:08] "GET /api/imagehost/config HTTP/1.1" 200 -
2025-07-30 15:10:08,300 - INFO - 127.0.0.1 - - [30/Jul/2025 15:10:08] "GET /api/imagehost/history HTTP/1.1" 200 -
2025-07-30 15:10:09,110 - INFO - 127.0.0.1 - - [30/Jul/2025 15:10:09] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 15:10:09,534 - INFO - 127.0.0.1 - - [30/Jul/2025 15:10:09] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 15:23:11,193 - INFO - 127.0.0.1 - - [30/Jul/2025 15:23:11] "GET /api/imagehost/config HTTP/1.1" 200 -
2025-07-30 15:23:11,195 - INFO - 127.0.0.1 - - [30/Jul/2025 15:23:11] "GET /api/imagehost/history HTTP/1.1" 200 -
2025-07-30 16:38:52,801 - INFO -  * Detected change in 'd:\\BaiduSyncdisk\\YouTube\\wcs_util\\auto_image\\app.py', reloading
2025-07-30 16:38:53,026 - INFO -  * Restarting with stat
2025-07-30 16:38:53,989 - WARNING -  * Debugger is active!
2025-07-30 16:38:53,995 - INFO -  * Debugger PIN: 536-538-330
2025-07-30 16:39:23,154 - INFO -  * Detected change in 'd:\\BaiduSyncdisk\\YouTube\\wcs_util\\auto_image\\app.py', reloading
2025-07-30 16:39:23,233 - INFO -  * Restarting with stat
2025-07-30 16:39:23,888 - WARNING -  * Debugger is active!
2025-07-30 16:39:23,894 - INFO -  * Debugger PIN: 536-538-330
2025-07-30 16:40:01,872 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-07-30 16:40:01,873 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 16:40:01,874 - INFO -  * Restarting with stat
2025-07-30 16:40:02,528 - WARNING -  * Debugger is active!
2025-07-30 16:40:02,533 - INFO -  * Debugger PIN: 536-538-330
2025-07-30 16:40:14,534 - INFO - 127.0.0.1 - - [30/Jul/2025 16:40:14] "GET / HTTP/1.1" 200 -
2025-07-30 16:40:14,638 - INFO - 127.0.0.1 - - [30/Jul/2025 16:40:14] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-30 16:40:14,641 - INFO - 127.0.0.1 - - [30/Jul/2025 16:40:14] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-30 16:40:14,703 - INFO - 127.0.0.1 - - [30/Jul/2025 16:40:14] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 16:40:14,775 - INFO - 127.0.0.1 - - [30/Jul/2025 16:40:14] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 16:40:14,776 - INFO - 127.0.0.1 - - [30/Jul/2025 16:40:14] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 16:40:15,137 - INFO - 127.0.0.1 - - [30/Jul/2025 16:40:15] "GET /api/config/save-settings HTTP/1.1" 200 -
2025-07-30 16:46:59,481 - INFO - 127.0.0.1 - - [30/Jul/2025 16:46:59] "GET / HTTP/1.1" 200 -
2025-07-30 16:46:59,568 - INFO - 127.0.0.1 - - [30/Jul/2025 16:46:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-30 16:46:59,572 - INFO - 127.0.0.1 - - [30/Jul/2025 16:46:59] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-30 16:46:59,668 - INFO - 127.0.0.1 - - [30/Jul/2025 16:46:59] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 16:46:59,922 - INFO - 127.0.0.1 - - [30/Jul/2025 16:46:59] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 16:46:59,938 - INFO - 127.0.0.1 - - [30/Jul/2025 16:46:59] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 16:46:59,984 - INFO - 127.0.0.1 - - [30/Jul/2025 16:46:59] "GET /api/config/save-settings HTTP/1.1" 200 -
2025-07-30 16:47:01,320 - INFO - 127.0.0.1 - - [30/Jul/2025 16:47:01] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 16:47:08,196 - INFO - Excel viewer file uploaded: 冰块遗漏分镜.xlsx
2025-07-30 16:47:08,197 - ERROR - Error uploading Excel viewer file: Object of type int64 is not JSON serializable
2025-07-30 16:47:08,198 - INFO - 127.0.0.1 - - [30/Jul/2025 16:47:08] "[35m[1mPOST /api/excel-viewer/upload HTTP/1.1[0m" 500 -
2025-07-30 16:51:18,521 - INFO - 127.0.0.1 - - [30/Jul/2025 16:51:18] "GET / HTTP/1.1" 200 -
2025-07-30 16:51:18,559 - INFO - 127.0.0.1 - - [30/Jul/2025 16:51:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-30 16:51:18,567 - INFO - 127.0.0.1 - - [30/Jul/2025 16:51:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-30 16:51:18,635 - INFO - 127.0.0.1 - - [30/Jul/2025 16:51:18] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 16:51:18,954 - INFO - 127.0.0.1 - - [30/Jul/2025 16:51:18] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 16:51:18,955 - INFO - 127.0.0.1 - - [30/Jul/2025 16:51:18] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 16:51:19,016 - INFO - 127.0.0.1 - - [30/Jul/2025 16:51:19] "GET /api/config/save-settings HTTP/1.1" 200 -
2025-07-30 16:51:19,684 - INFO - 127.0.0.1 - - [30/Jul/2025 16:51:19] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 16:51:23,246 - INFO - Excel viewer file uploaded: 冰块遗漏分镜.xlsx
2025-07-30 16:51:23,246 - ERROR - Error uploading Excel viewer file: Object of type int64 is not JSON serializable
2025-07-30 16:51:23,247 - INFO - 127.0.0.1 - - [30/Jul/2025 16:51:23] "[35m[1mPOST /api/excel-viewer/upload HTTP/1.1[0m" 500 -
2025-07-30 16:52:12,828 - INFO -  * Detected change in 'd:\\BaiduSyncdisk\\YouTube\\wcs_util\\auto_image\\modules\\excel_viewer.py', reloading
2025-07-30 16:52:12,965 - INFO -  * Restarting with stat
2025-07-30 16:52:13,162 - INFO -  * Detected change in 'D:\\BaiduSyncdisk\\YouTube\\wcs_util\\auto_image\\modules\\excel_viewer.py', reloading
2025-07-30 16:52:13,258 - INFO -  * Restarting with stat
2025-07-30 16:52:13,771 - WARNING -  * Debugger is active!
2025-07-30 16:52:13,781 - INFO -  * Debugger PIN: 536-538-330
2025-07-30 16:52:14,003 - WARNING -  * Debugger is active!
2025-07-30 16:52:14,010 - INFO -  * Debugger PIN: 536-538-330
2025-07-30 16:53:39,455 - INFO -  * Detected change in 'D:\\BaiduSyncdisk\\YouTube\\wcs_util\\auto_image\\app.py', reloading
2025-07-30 16:53:39,566 - INFO -  * Restarting with stat
2025-07-30 16:53:40,216 - INFO -  * Detected change in 'd:\\BaiduSyncdisk\\YouTube\\wcs_util\\auto_image\\app.py', reloading
2025-07-30 16:53:40,323 - INFO -  * Restarting with stat
2025-07-30 16:53:40,409 - WARNING -  * Debugger is active!
2025-07-30 16:53:40,417 - INFO -  * Debugger PIN: 536-538-330
2025-07-30 16:53:41,045 - WARNING -  * Debugger is active!
2025-07-30 16:53:41,051 - INFO -  * Debugger PIN: 536-538-330
2025-07-30 16:53:50,484 - INFO -  * Detected change in 'D:\\BaiduSyncdisk\\YouTube\\wcs_util\\auto_image\\app.py', reloading
2025-07-30 16:53:50,602 - INFO -  * Restarting with stat
2025-07-30 16:53:51,113 - INFO -  * Detected change in 'd:\\BaiduSyncdisk\\YouTube\\wcs_util\\auto_image\\app.py', reloading
2025-07-30 16:53:51,218 - INFO -  * Restarting with stat
2025-07-30 16:53:51,295 - WARNING -  * Debugger is active!
2025-07-30 16:53:51,301 - INFO -  * Debugger PIN: 536-538-330
2025-07-30 16:53:51,918 - WARNING -  * Debugger is active!
2025-07-30 16:53:51,924 - INFO -  * Debugger PIN: 536-538-330
2025-07-30 16:57:18,191 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:18] "GET / HTTP/1.1" 200 -
2025-07-30 16:57:18,308 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:18] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-30 16:57:18,309 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-30 16:57:18,329 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:18] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 16:57:18,371 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:18] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 16:57:18,372 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:18] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 16:57:18,498 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:18] "GET /api/config/save-settings HTTP/1.1" 200 -
2025-07-30 16:57:22,103 - INFO - Excel viewer file uploaded: 冰块遗漏分镜.xlsx
2025-07-30 16:57:22,103 - ERROR - Error uploading Excel viewer file: Object of type int64 is not JSON serializable
2025-07-30 16:57:22,104 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:22] "[35m[1mPOST /api/excel-viewer/upload HTTP/1.1[0m" 500 -
2025-07-30 16:57:29,846 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
2025-07-30 16:57:29,846 - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 16:57:29,848 - INFO -  * Restarting with stat
2025-07-30 16:57:30,537 - WARNING -  * Debugger is active!
2025-07-30 16:57:30,543 - INFO -  * Debugger PIN: 536-538-330
2025-07-30 16:57:31,249 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:31] "GET / HTTP/1.1" 200 -
2025-07-30 16:57:31,344 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:31] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-30 16:57:31,345 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:31] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-30 16:57:31,370 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:31] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 16:57:31,498 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:31] "GET /api/config/models HTTP/1.1" 200 -
2025-07-30 16:57:31,499 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:31] "GET /api/config/characters HTTP/1.1" 200 -
2025-07-30 16:57:31,549 - INFO - 127.0.0.1 - - [30/Jul/2025 16:57:31] "GET /api/config/save-settings HTTP/1.1" 200 -
